package com.cloudpod.podsail.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.common.base.response.Response;
import com.cloudpod.podsail.dto.instance.UserServerInstanceCreateDTO;
import com.cloudpod.podsail.dto.instance.UserServerInstanceQueryDTO;
import com.cloudpod.podsail.dto.instance.UserServerInstanceResponseDTO;
import com.cloudpod.podsail.dto.instance.UserServerInstanceUpdateDTO;
import com.cloudpod.podsail.service.UserServerInstanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 用户服务器实例管理控制器
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/user-server-instances")
@Api(tags = "用户服务器实例管理", description = "用户服务器实例相关的CRUD操作接口")
public class UserServerInstanceController {

    @Autowired
    private UserServerInstanceService userServerInstanceService;

    @PostMapping
    @ApiOperation(value = "创建用户服务器实例", notes = "创建新的用户服务器实例")
    public Response<UserServerInstanceResponseDTO> createUserServerInstance(
            @ApiParam(value = "用户服务器实例创建信息", required = true)
            @RequestBody @Valid UserServerInstanceCreateDTO createDTO) {
        
        log.info("创建用户服务器实例，请求参数: {}", createDTO);
        UserServerInstanceResponseDTO responseDTO = userServerInstanceService.createUserServerInstance(createDTO);
        return Response.success(responseDTO);
    }

    @PutMapping
    @ApiOperation(value = "更新用户服务器实例", notes = "更新用户服务器实例信息")
    public Response<UserServerInstanceResponseDTO> updateUserServerInstance(
            @ApiParam(value = "用户服务器实例更新信息", required = true)
            @RequestBody @Valid UserServerInstanceUpdateDTO updateDTO) {
        
        log.info("更新用户服务器实例，请求参数: {}", updateDTO);
        UserServerInstanceResponseDTO responseDTO = userServerInstanceService.updateUserServerInstance(updateDTO);
        return Response.success(responseDTO);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询用户服务器实例", notes = "根据用户服务器实例ID查询详细信息")
    public Response<UserServerInstanceResponseDTO> getUserServerInstanceById(
            @ApiParam(value = "用户服务器实例ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("根据ID查询用户服务器实例，ID: {}", id);
        UserServerInstanceResponseDTO responseDTO = userServerInstanceService.getUserServerInstanceById(id);
        return Response.success(responseDTO);
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除用户服务器实例", notes = "根据用户服务器实例ID删除（逻辑删除）")
    public Response<Boolean> deleteUserServerInstance(
            @ApiParam(value = "用户服务器实例ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("删除用户服务器实例，ID: {}", id);
        boolean result = userServerInstanceService.deleteEntityById(id);
        return Response.success(result);
    }

    @DeleteMapping("/batch")
    @ApiOperation(value = "批量删除用户服务器实例", notes = "根据用户服务器实例ID列表批量删除（逻辑删除）")
    public Response<Boolean> deleteUserServerInstances(
            @ApiParam(value = "用户服务器实例ID列表", required = true)
            @RequestBody @Valid List<Long> ids) {
        
        log.info("批量删除用户服务器实例，ID列表: {}", ids);
        boolean result = userServerInstanceService.deleteEntitiesByIds(ids);
        return Response.success(result);
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页查询用户服务器实例", notes = "根据条件分页查询用户服务器实例列表")
    public Response<IPage<UserServerInstanceResponseDTO>> getUserServerInstancePage(
            @ApiParam(value = "查询条件", required = true)
            @RequestBody @Valid UserServerInstanceQueryDTO queryDTO) {
        
        log.info("分页查询用户服务器实例，查询条件: {}", queryDTO);
        IPage<UserServerInstanceResponseDTO> page = userServerInstanceService.getUserServerInstancePage(queryDTO);
        return Response.success(page);
    }

    @PostMapping("/list")
    @ApiOperation(value = "查询用户服务器实例列表", notes = "根据条件查询用户服务器实例列表")
    public Response<List<UserServerInstanceResponseDTO>> getUserServerInstanceList(
            @ApiParam(value = "查询条件")
            @RequestBody UserServerInstanceQueryDTO queryDTO) {
        
        log.info("查询用户服务器实例列表，查询条件: {}", queryDTO);
        List<UserServerInstanceResponseDTO> list = userServerInstanceService.getUserServerInstanceList(queryDTO);
        return Response.success(list);
    }

    @GetMapping("/server-id/{serverId}")
    @ApiOperation(value = "根据服务器实例ID查询", notes = "根据服务器实例ID查询用户服务器实例信息")
    public Response<UserServerInstanceResponseDTO> getUserServerInstanceByServerId(
            @ApiParam(value = "服务器实例ID", required = true)
            @PathVariable("serverId") @NotBlank String serverId) {
        
        log.info("根据服务器实例ID查询，服务器实例ID: {}", serverId);
        var userServerInstance = userServerInstanceService.getUserServerInstanceByServerId(serverId);
        if (userServerInstance == null) {
            return Response.success(null);
        }
        UserServerInstanceResponseDTO responseDTO = userServerInstanceService.getUserServerInstanceById(userServerInstance.getId());
        return Response.success(responseDTO);
    }

    @GetMapping("/user/{userId}")
    @ApiOperation(value = "根据用户ID查询服务器实例", notes = "查询指定用户的所有服务器实例")
    public Response<List<UserServerInstanceResponseDTO>> getUserServerInstancesByUserId(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {
        
        log.info("根据用户ID查询服务器实例，用户ID: {}", userId);
        var userServerInstances = userServerInstanceService.getUserServerInstancesByUserId(userId);
        List<UserServerInstanceResponseDTO> responseDTOList = userServerInstances.stream()
                .map(userServerInstance -> userServerInstanceService.getUserServerInstanceById(userServerInstance.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/user/{userId}/status/{instanceStatus}")
    @ApiOperation(value = "根据用户ID和状态查询服务器实例", notes = "查询指定用户指定状态的服务器实例列表")
    public Response<List<UserServerInstanceResponseDTO>> getUserServerInstancesByUserIdAndStatus(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId,
            @ApiParam(value = "实例状态", required = true, notes = "1-创建中 2-运行中 3-已停止 4-已销毁")
            @PathVariable("instanceStatus") @NotNull Integer instanceStatus) {
        
        log.info("根据用户ID和状态查询服务器实例，用户ID: {}, 实例状态: {}", userId, instanceStatus);
        var userServerInstances = userServerInstanceService.getUserServerInstancesByUserIdAndStatus(userId, instanceStatus);
        List<UserServerInstanceResponseDTO> responseDTOList = userServerInstances.stream()
                .map(userServerInstance -> userServerInstanceService.getUserServerInstanceById(userServerInstance.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/order/{orderId}")
    @ApiOperation(value = "根据订单ID查询服务器实例", notes = "查询指定订单的所有服务器实例")
    public Response<List<UserServerInstanceResponseDTO>> getUserServerInstancesByOrderId(
            @ApiParam(value = "订单ID", required = true)
            @PathVariable("orderId") @NotNull Long orderId) {
        
        log.info("根据订单ID查询服务器实例，订单ID: {}", orderId);
        var userServerInstances = userServerInstanceService.getUserServerInstancesByOrderId(orderId);
        List<UserServerInstanceResponseDTO> responseDTOList = userServerInstances.stream()
                .map(userServerInstance -> userServerInstanceService.getUserServerInstanceById(userServerInstance.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/region/{regionId}")
    @ApiOperation(value = "根据区域ID查询服务器实例", notes = "查询指定区域的所有服务器实例")
    public Response<List<UserServerInstanceResponseDTO>> getUserServerInstancesByRegionId(
            @ApiParam(value = "区域ID", required = true)
            @PathVariable("regionId") @NotBlank String regionId) {
        
        log.info("根据区域ID查询服务器实例，区域ID: {}", regionId);
        var userServerInstances = userServerInstanceService.getUserServerInstancesByRegionId(regionId);
        List<UserServerInstanceResponseDTO> responseDTOList = userServerInstances.stream()
                .map(userServerInstance -> userServerInstanceService.getUserServerInstanceById(userServerInstance.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/region/{regionId}/zone/{zoneId}")
    @ApiOperation(value = "根据区域ID和可用区ID查询服务器实例", notes = "查询指定区域和可用区的服务器实例列表")
    public Response<List<UserServerInstanceResponseDTO>> getUserServerInstancesByRegionIdAndZoneId(
            @ApiParam(value = "区域ID", required = true)
            @PathVariable("regionId") @NotBlank String regionId,
            @ApiParam(value = "可用区ID", required = true)
            @PathVariable("zoneId") @NotBlank String zoneId) {
        
        log.info("根据区域ID和可用区ID查询服务器实例，区域ID: {}, 可用区ID: {}", regionId, zoneId);
        var userServerInstances = userServerInstanceService.getUserServerInstancesByRegionIdAndZoneId(regionId, zoneId);
        List<UserServerInstanceResponseDTO> responseDTOList = userServerInstances.stream()
                .map(userServerInstance -> userServerInstanceService.getUserServerInstanceById(userServerInstance.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/exists/{serverId}")
    @ApiOperation(value = "检查服务器实例ID是否存在", notes = "检查指定服务器实例ID是否已存在")
    public Response<Boolean> existsByServerId(
            @ApiParam(value = "服务器实例ID", required = true)
            @PathVariable("serverId") @NotBlank String serverId) {
        
        log.info("检查服务器实例ID是否存在，服务器实例ID: {}", serverId);
        boolean exists = userServerInstanceService.existsByServerId(serverId);
        return Response.success(exists);
    }

    @PostMapping("/{id}/start")
    @ApiOperation(value = "启动服务器实例", notes = "启动指定的服务器实例")
    public Response<Boolean> startServerInstance(
            @ApiParam(value = "用户服务器实例ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("启动服务器实例，实例ID: {}", id);
        boolean result = userServerInstanceService.startServerInstance(id);
        return Response.success(result);
    }

    @PostMapping("/{id}/stop")
    @ApiOperation(value = "停止服务器实例", notes = "停止指定的服务器实例")
    public Response<Boolean> stopServerInstance(
            @ApiParam(value = "用户服务器实例ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("停止服务器实例，实例ID: {}", id);
        boolean result = userServerInstanceService.stopServerInstance(id);
        return Response.success(result);
    }

    @PostMapping("/{id}/destroy")
    @ApiOperation(value = "销毁服务器实例", notes = "销毁指定的服务器实例")
    public Response<Boolean> destroyServerInstance(
            @ApiParam(value = "用户服务器实例ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("销毁服务器实例，实例ID: {}", id);
        boolean result = userServerInstanceService.destroyServerInstance(id);
        return Response.success(result);
    }

    @PostMapping("/{id}/renew")
    @ApiOperation(value = "续费服务器实例", notes = "续费指定的服务器实例")
    public Response<Boolean> renewServerInstance(
            @ApiParam(value = "用户服务器实例ID", required = true)
            @PathVariable("id") @NotNull Long id,
            @ApiParam(value = "新的到期时间", required = true)
            @RequestParam("expireTime") @NotNull Long expireTime) {
        
        log.info("续费服务器实例，实例ID: {}, 新到期时间: {}", id, expireTime);
        boolean result = userServerInstanceService.renewServerInstance(id, expireTime);
        return Response.success(result);
    }

    @PostMapping("/{id}/auto-renew")
    @ApiOperation(value = "设置自动续费", notes = "设置服务器实例的自动续费状态")
    public Response<Boolean> setAutoRenew(
            @ApiParam(value = "用户服务器实例ID", required = true)
            @PathVariable("id") @NotNull Long id,
            @ApiParam(value = "是否自动续费", required = true, notes = "0-否 1-是")
            @RequestParam("autoRenew") @NotNull Integer autoRenew) {
        
        log.info("设置自动续费，实例ID: {}, 自动续费: {}", id, autoRenew);
        boolean result = userServerInstanceService.setAutoRenew(id, autoRenew);
        return Response.success(result);
    }

    @GetMapping("/expiring/{days}")
    @ApiOperation(value = "查询即将到期的服务器实例", notes = "查询指定天数内即将到期的服务器实例")
    public Response<List<UserServerInstanceResponseDTO>> getExpiringServerInstances(
            @ApiParam(value = "提前天数", required = true)
            @PathVariable("days") @Min(value = 1, message = "提前天数必须大于0") Integer days) {

        log.info("查询即将到期的服务器实例，提前天数: {}", days);
        var expiringInstances = userServerInstanceService.getExpiringServerInstances(days);
        List<UserServerInstanceResponseDTO> responseDTOList = expiringInstances.stream()
                .map(userServerInstance -> userServerInstanceService.getUserServerInstanceById(userServerInstance.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/expired")
    @ApiOperation(value = "查询已过期的服务器实例", notes = "查询所有已过期的服务器实例")
    public Response<List<UserServerInstanceResponseDTO>> getExpiredServerInstances() {

        log.info("查询已过期的服务器实例");
        var expiredInstances = userServerInstanceService.getExpiredServerInstances();
        List<UserServerInstanceResponseDTO> responseDTOList = expiredInstances.stream()
                .map(userServerInstance -> userServerInstanceService.getUserServerInstanceById(userServerInstance.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/auto-renew")
    @ApiOperation(value = "查询需要自动续费的服务器实例", notes = "查询所有设置了自动续费的服务器实例")
    public Response<List<UserServerInstanceResponseDTO>> getAutoRenewServerInstances() {

        log.info("查询需要自动续费的服务器实例");
        var autoRenewInstances = userServerInstanceService.getAutoRenewServerInstances();
        List<UserServerInstanceResponseDTO> responseDTOList = autoRenewInstances.stream()
                .map(userServerInstance -> userServerInstanceService.getUserServerInstanceById(userServerInstance.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/user/{userId}/count")
    @ApiOperation(value = "统计用户服务器实例数量", notes = "统计指定用户的服务器实例总数量")
    public Response<Long> countUserServerInstances(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {

        log.info("统计用户服务器实例数量，用户ID: {}", userId);
        long count = userServerInstanceService.countUserServerInstances(userId);
        return Response.success(count);
    }

    @GetMapping("/user/{userId}/running-count")
    @ApiOperation(value = "统计用户运行中的服务器实例数量", notes = "统计指定用户运行中的服务器实例数量")
    public Response<Long> countUserRunningServerInstances(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {

        log.info("统计用户运行中的服务器实例数量，用户ID: {}", userId);
        long count = userServerInstanceService.countUserRunningServerInstances(userId);
        return Response.success(count);
    }

    @GetMapping("/user/{userId}/stopped-count")
    @ApiOperation(value = "统计用户已停止的服务器实例数量", notes = "统计指定用户已停止的服务器实例数量")
    public Response<Long> countUserStoppedServerInstances(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {

        log.info("统计用户已停止的服务器实例数量，用户ID: {}", userId);
        long count = userServerInstanceService.countUserStoppedServerInstances(userId);
        return Response.success(count);
    }
}
