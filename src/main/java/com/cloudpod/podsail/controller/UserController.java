package com.cloudpod.podsail.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.common.base.response.Response;
import com.cloudpod.podsail.dto.user.UserCreateDTO;
import com.cloudpod.podsail.dto.user.UserQueryDTO;
import com.cloudpod.podsail.dto.user.UserResponseDTO;
import com.cloudpod.podsail.dto.user.UserUpdateDTO;
import com.cloudpod.podsail.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 用户管理控制器
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/users")
@Api(tags = "用户管理", description = "用户相关的CRUD操作接口")
public class UserController {

    @Autowired
    private UserService userService;

    @PostMapping
    @ApiOperation(value = "创建用户", notes = "创建新用户")
    public Response<UserResponseDTO> createUser(
            @ApiParam(value = "用户创建信息", required = true)
            @RequestBody @Valid UserCreateDTO createDTO) {
        
        log.info("创建用户，请求参数: {}", createDTO);
        UserResponseDTO responseDTO = userService.createUser(createDTO);
        return Response.success(responseDTO);
    }

    @PutMapping
    @ApiOperation(value = "更新用户", notes = "更新用户信息")
    public Response<UserResponseDTO> updateUser(
            @ApiParam(value = "用户更新信息", required = true)
            @RequestBody @Valid UserUpdateDTO updateDTO) {
        
        log.info("更新用户，请求参数: {}", updateDTO);
        UserResponseDTO responseDTO = userService.updateUser(updateDTO);
        return Response.success(responseDTO);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询用户", notes = "根据用户ID查询用户详细信息")
    public Response<UserResponseDTO> getUserById(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("根据ID查询用户，用户ID: {}", id);
        UserResponseDTO responseDTO = userService.getUserById(id);
        return Response.success(responseDTO);
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除用户", notes = "根据用户ID删除用户（逻辑删除）")
    public Response<Boolean> deleteUser(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("删除用户，用户ID: {}", id);
        boolean result = userService.deleteEntityById(id);
        return Response.success(result);
    }

    @DeleteMapping("/batch")
    @ApiOperation(value = "批量删除用户", notes = "根据用户ID列表批量删除用户（逻辑删除）")
    public Response<Boolean> deleteUsers(
            @ApiParam(value = "用户ID列表", required = true)
            @RequestBody @Valid List<Long> ids) {
        
        log.info("批量删除用户，用户ID列表: {}", ids);
        boolean result = userService.deleteEntitiesByIds(ids);
        return Response.success(result);
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页查询用户", notes = "根据条件分页查询用户列表")
    public Response<IPage<UserResponseDTO>> getUserPage(
            @ApiParam(value = "查询条件", required = true)
            @RequestBody @Valid UserQueryDTO queryDTO) {
        
        log.info("分页查询用户，查询条件: {}", queryDTO);
        IPage<UserResponseDTO> page = userService.getUserPage(queryDTO);
        return Response.success(page);
    }

    @PostMapping("/list")
    @ApiOperation(value = "查询用户列表", notes = "根据条件查询用户列表")
    public Response<List<UserResponseDTO>> getUserList(
            @ApiParam(value = "查询条件")
            @RequestBody UserQueryDTO queryDTO) {
        
        log.info("查询用户列表，查询条件: {}", queryDTO);
        List<UserResponseDTO> list = userService.getUserList(queryDTO);
        return Response.success(list);
    }

    @GetMapping("/username/{username}")
    @ApiOperation(value = "根据用户名查询用户", notes = "根据用户名查询用户信息")
    public Response<UserResponseDTO> getUserByUsername(
            @ApiParam(value = "用户名", required = true)
            @PathVariable("username") @NotBlank String username) {
        
        log.info("根据用户名查询用户，用户名: {}", username);
        var user = userService.getUserByUsername(username);
        if (user == null) {
            return Response.success(null);
        }
        UserResponseDTO responseDTO = userService.getUserById(user.getId());
        return Response.success(responseDTO);
    }

    @GetMapping("/code/{code}")
    @ApiOperation(value = "根据用户码查询用户", notes = "根据用户唯一码查询用户信息")
    public Response<UserResponseDTO> getUserByCode(
            @ApiParam(value = "用户唯一码", required = true)
            @PathVariable("code") @NotBlank String code) {
        
        log.info("根据用户码查询用户，用户码: {}", code);
        var user = userService.getUserByCode(code);
        if (user == null) {
            return Response.success(null);
        }
        UserResponseDTO responseDTO = userService.getUserById(user.getId());
        return Response.success(responseDTO);
    }

    @GetMapping("/exists/username/{username}")
    @ApiOperation(value = "检查用户名是否存在", notes = "检查指定用户名是否已被使用")
    public Response<Boolean> existsByUsername(
            @ApiParam(value = "用户名", required = true)
            @PathVariable("username") @NotBlank String username) {
        
        log.info("检查用户名是否存在，用户名: {}", username);
        boolean exists = userService.existsByUsername(username);
        return Response.success(exists);
    }

    @GetMapping("/exists/code/{code}")
    @ApiOperation(value = "检查用户码是否存在", notes = "检查指定用户唯一码是否已被使用")
    public Response<Boolean> existsByCode(
            @ApiParam(value = "用户唯一码", required = true)
            @PathVariable("code") @NotBlank String code) {
        
        log.info("检查用户码是否存在，用户码: {}", code);
        boolean exists = userService.existsByCode(code);
        return Response.success(exists);
    }

    @PostMapping("/{id}/password")
    @ApiOperation(value = "更新用户密码", notes = "更新指定用户的密码")
    public Response<Boolean> updatePassword(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("id") @NotNull Long id,
            @ApiParam(value = "新密码", required = true)
            @RequestParam("newPassword") @NotBlank String newPassword) {
        
        log.info("更新用户密码，用户ID: {}", id);
        boolean result = userService.updatePassword(id, newPassword);
        return Response.success(result);
    }

    @PostMapping("/{id}/balance")
    @ApiOperation(value = "更新用户余额", notes = "增加或减少用户账户余额")
    public Response<Boolean> updateBalance(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("id") @NotNull Long id,
            @ApiParam(value = "金额变动（正数为增加，负数为减少）", required = true)
            @RequestParam("amount") @NotNull BigDecimal amount) {
        
        log.info("更新用户余额，用户ID: {}, 金额变动: {}", id, amount);
        boolean result = userService.updateBalance(id, amount);
        return Response.success(result);
    }

    @PostMapping("/validate")
    @ApiOperation(value = "验证用户密码", notes = "验证用户名和密码是否匹配")
    public Response<Boolean> validatePassword(
            @ApiParam(value = "用户名", required = true)
            @RequestParam("username") @NotBlank String username,
            @ApiParam(value = "密码", required = true)
            @RequestParam("password") @NotBlank String password) {
        
        log.info("验证用户密码，用户名: {}", username);
        boolean valid = userService.validatePassword(username, password);
        return Response.success(valid);
    }
}
