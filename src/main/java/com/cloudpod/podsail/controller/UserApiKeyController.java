package com.cloudpod.podsail.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.common.base.response.Response;
import com.cloudpod.podsail.dto.user.UserApiKeyCreateDTO;
import com.cloudpod.podsail.dto.user.UserApiKeyQueryDTO;
import com.cloudpod.podsail.dto.user.UserApiKeyResponseDTO;
import com.cloudpod.podsail.dto.user.UserApiKeyUpdateDTO;
import com.cloudpod.podsail.service.UserApiKeyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 用户API密钥管理控制器
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/user-api-keys")
@Api(tags = "用户API密钥管理", description = "用户API密钥相关的CRUD操作接口")
public class UserApiKeyController {

    @Autowired
    private UserApiKeyService userApiKeyService;

    @PostMapping
    @ApiOperation(value = "创建用户API密钥", notes = "为用户创建新的API密钥")
    public Response<UserApiKeyResponseDTO> createUserApiKey(
            @ApiParam(value = "用户API密钥创建信息", required = true)
            @RequestBody @Valid UserApiKeyCreateDTO createDTO) {
        
        log.info("创建用户API密钥，请求参数: {}", createDTO);
        UserApiKeyResponseDTO responseDTO = userApiKeyService.createUserApiKey(createDTO);
        return Response.success(responseDTO);
    }

    @PutMapping
    @ApiOperation(value = "更新用户API密钥", notes = "更新用户API密钥信息")
    public Response<UserApiKeyResponseDTO> updateUserApiKey(
            @ApiParam(value = "用户API密钥更新信息", required = true)
            @RequestBody @Valid UserApiKeyUpdateDTO updateDTO) {
        
        log.info("更新用户API密钥，请求参数: {}", updateDTO);
        UserApiKeyResponseDTO responseDTO = userApiKeyService.updateUserApiKey(updateDTO);
        return Response.success(responseDTO);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询用户API密钥", notes = "根据用户API密钥ID查询详细信息")
    public Response<UserApiKeyResponseDTO> getUserApiKeyById(
            @ApiParam(value = "用户API密钥ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("根据ID查询用户API密钥，ID: {}", id);
        UserApiKeyResponseDTO responseDTO = userApiKeyService.getUserApiKeyById(id);
        return Response.success(responseDTO);
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除用户API密钥", notes = "根据用户API密钥ID删除（逻辑删除）")
    public Response<Boolean> deleteUserApiKey(
            @ApiParam(value = "用户API密钥ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("删除用户API密钥，ID: {}", id);
        boolean result = userApiKeyService.deleteEntityById(id);
        return Response.success(result);
    }

    @DeleteMapping("/batch")
    @ApiOperation(value = "批量删除用户API密钥", notes = "根据用户API密钥ID列表批量删除（逻辑删除）")
    public Response<Boolean> deleteUserApiKeys(
            @ApiParam(value = "用户API密钥ID列表", required = true)
            @RequestBody @Valid List<Long> ids) {
        
        log.info("批量删除用户API密钥，ID列表: {}", ids);
        boolean result = userApiKeyService.deleteEntitiesByIds(ids);
        return Response.success(result);
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页查询用户API密钥", notes = "根据条件分页查询用户API密钥列表")
    public Response<IPage<UserApiKeyResponseDTO>> getUserApiKeyPage(
            @ApiParam(value = "查询条件", required = true)
            @RequestBody @Valid UserApiKeyQueryDTO queryDTO) {
        
        log.info("分页查询用户API密钥，查询条件: {}", queryDTO);
        IPage<UserApiKeyResponseDTO> page = userApiKeyService.getUserApiKeyPage(queryDTO);
        return Response.success(page);
    }

    @PostMapping("/list")
    @ApiOperation(value = "查询用户API密钥列表", notes = "根据条件查询用户API密钥列表")
    public Response<List<UserApiKeyResponseDTO>> getUserApiKeyList(
            @ApiParam(value = "查询条件")
            @RequestBody UserApiKeyQueryDTO queryDTO) {
        
        log.info("查询用户API密钥列表，查询条件: {}", queryDTO);
        List<UserApiKeyResponseDTO> list = userApiKeyService.getUserApiKeyList(queryDTO);
        return Response.success(list);
    }

    @GetMapping("/user/{userId}")
    @ApiOperation(value = "根据用户ID查询API密钥列表", notes = "查询指定用户的所有API密钥")
    public Response<List<UserApiKeyResponseDTO>> getUserApiKeysByUserId(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {
        
        log.info("根据用户ID查询API密钥列表，用户ID: {}", userId);
        var userApiKeys = userApiKeyService.getUserApiKeysByUserId(userId);
        List<UserApiKeyResponseDTO> responseDTOList = userApiKeys.stream()
                .map(userApiKey -> userApiKeyService.getUserApiKeyById(userApiKey.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/api-key/{apiKey}")
    @ApiOperation(value = "根据API密钥查询", notes = "根据API密钥查询用户API密钥信息")
    public Response<UserApiKeyResponseDTO> getUserApiKeyByApiKey(
            @ApiParam(value = "API密钥", required = true)
            @PathVariable("apiKey") @NotBlank String apiKey) {
        
        log.info("根据API密钥查询，API密钥: {}", apiKey);
        var userApiKey = userApiKeyService.getUserApiKeyByApiKey(apiKey);
        if (userApiKey == null) {
            return Response.success(null);
        }
        UserApiKeyResponseDTO responseDTO = userApiKeyService.getUserApiKeyById(userApiKey.getId());
        return Response.success(responseDTO);
    }

    @GetMapping("/exists/{apiKey}")
    @ApiOperation(value = "检查API密钥是否存在", notes = "检查指定API密钥是否已存在")
    public Response<Boolean> existsByApiKey(
            @ApiParam(value = "API密钥", required = true)
            @PathVariable("apiKey") @NotBlank String apiKey) {
        
        log.info("检查API密钥是否存在，API密钥: {}", apiKey);
        boolean exists = userApiKeyService.existsByApiKey(apiKey);
        return Response.success(exists);
    }

    @PostMapping("/{id}/revoke")
    @ApiOperation(value = "吊销API密钥", notes = "吊销指定的API密钥")
    public Response<Boolean> revokeUserApiKey(
            @ApiParam(value = "用户API密钥ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("吊销API密钥，ID: {}", id);
        boolean result = userApiKeyService.revokeUserApiKey(id);
        return Response.success(result);
    }

    @PostMapping("/{id}/activate")
    @ApiOperation(value = "激活API密钥", notes = "激活指定的API密钥")
    public Response<Boolean> activateUserApiKey(
            @ApiParam(value = "用户API密钥ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("激活API密钥，ID: {}", id);
        boolean result = userApiKeyService.activateUserApiKey(id);
        return Response.success(result);
    }

    @PostMapping("/validate/{apiKey}")
    @ApiOperation(value = "验证API密钥", notes = "验证API密钥是否有效")
    public Response<Boolean> validateApiKey(
            @ApiParam(value = "API密钥", required = true)
            @PathVariable("apiKey") @NotBlank String apiKey) {
        
        log.info("验证API密钥，API密钥: {}", apiKey);
        boolean valid = userApiKeyService.validateApiKey(apiKey);
        return Response.success(valid);
    }

    @PostMapping("/{id}/regenerate")
    @ApiOperation(value = "重新生成API密钥", notes = "重新生成指定的API密钥")
    public Response<String> regenerateApiKey(
            @ApiParam(value = "用户API密钥ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("重新生成API密钥，ID: {}", id);
        String newApiKey = userApiKeyService.regenerateApiKey(id);
        return Response.success(newApiKey);
    }

    @PostMapping("/update-visit-time/{apiKey}")
    @ApiOperation(value = "更新访问时间", notes = "更新API密钥的最近访问时间")
    public Response<Boolean> updateLastVisitTime(
            @ApiParam(value = "API密钥", required = true)
            @PathVariable("apiKey") @NotBlank String apiKey) {
        
        log.info("更新访问时间，API密钥: {}", apiKey);
        boolean result = userApiKeyService.updateLastVisitTime(apiKey);
        return Response.success(result);
    }
}
