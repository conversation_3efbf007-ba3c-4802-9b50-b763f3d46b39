package com.cloudpod.podsail.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.common.base.response.Response;
import com.cloudpod.podsail.dto.order.UserOrderCreateDTO;
import com.cloudpod.podsail.dto.order.UserOrderQueryDTO;
import com.cloudpod.podsail.dto.order.UserOrderResponseDTO;
import com.cloudpod.podsail.dto.order.UserOrderUpdateDTO;
import com.cloudpod.podsail.service.UserOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 用户订单管理控制器
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/user-orders")
@Api(tags = "用户订单管理", description = "用户订单相关的CRUD操作接口")
public class UserOrderController {

    @Autowired
    private UserOrderService userOrderService;

    @PostMapping
    @ApiOperation(value = "创建用户订单", notes = "创建新的用户订单")
    public Response<UserOrderResponseDTO> createUserOrder(
            @ApiParam(value = "用户订单创建信息", required = true)
            @RequestBody @Valid UserOrderCreateDTO createDTO) {
        
        log.info("创建用户订单，请求参数: {}", createDTO);
        UserOrderResponseDTO responseDTO = userOrderService.createUserOrder(createDTO);
        return Response.success(responseDTO);
    }

    @PutMapping
    @ApiOperation(value = "更新用户订单", notes = "更新用户订单信息")
    public Response<UserOrderResponseDTO> updateUserOrder(
            @ApiParam(value = "用户订单更新信息", required = true)
            @RequestBody @Valid UserOrderUpdateDTO updateDTO) {
        
        log.info("更新用户订单，请求参数: {}", updateDTO);
        UserOrderResponseDTO responseDTO = userOrderService.updateUserOrder(updateDTO);
        return Response.success(responseDTO);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询用户订单", notes = "根据用户订单ID查询详细信息")
    public Response<UserOrderResponseDTO> getUserOrderById(
            @ApiParam(value = "用户订单ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("根据ID查询用户订单，ID: {}", id);
        UserOrderResponseDTO responseDTO = userOrderService.getUserOrderById(id);
        return Response.success(responseDTO);
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除用户订单", notes = "根据用户订单ID删除（逻辑删除）")
    public Response<Boolean> deleteUserOrder(
            @ApiParam(value = "用户订单ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("删除用户订单，ID: {}", id);
        boolean result = userOrderService.deleteEntityById(id);
        return Response.success(result);
    }

    @DeleteMapping("/batch")
    @ApiOperation(value = "批量删除用户订单", notes = "根据用户订单ID列表批量删除（逻辑删除）")
    public Response<Boolean> deleteUserOrders(
            @ApiParam(value = "用户订单ID列表", required = true)
            @RequestBody @Valid List<Long> ids) {
        
        log.info("批量删除用户订单，ID列表: {}", ids);
        boolean result = userOrderService.deleteEntitiesByIds(ids);
        return Response.success(result);
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页查询用户订单", notes = "根据条件分页查询用户订单列表")
    public Response<IPage<UserOrderResponseDTO>> getUserOrderPage(
            @ApiParam(value = "查询条件", required = true)
            @RequestBody @Valid UserOrderQueryDTO queryDTO) {
        
        log.info("分页查询用户订单，查询条件: {}", queryDTO);
        IPage<UserOrderResponseDTO> page = userOrderService.getUserOrderPage(queryDTO);
        return Response.success(page);
    }

    @PostMapping("/list")
    @ApiOperation(value = "查询用户订单列表", notes = "根据条件查询用户订单列表")
    public Response<List<UserOrderResponseDTO>> getUserOrderList(
            @ApiParam(value = "查询条件")
            @RequestBody UserOrderQueryDTO queryDTO) {
        
        log.info("查询用户订单列表，查询条件: {}", queryDTO);
        List<UserOrderResponseDTO> list = userOrderService.getUserOrderList(queryDTO);
        return Response.success(list);
    }

    @GetMapping("/order-no/{orderNo}")
    @ApiOperation(value = "根据订单号查询订单", notes = "根据订单号查询用户订单信息")
    public Response<UserOrderResponseDTO> getUserOrderByOrderNo(
            @ApiParam(value = "订单号", required = true)
            @PathVariable("orderNo") @NotBlank String orderNo) {
        
        log.info("根据订单号查询订单，订单号: {}", orderNo);
        var userOrder = userOrderService.getUserOrderByOrderNo(orderNo);
        if (userOrder == null) {
            return Response.success(null);
        }
        UserOrderResponseDTO responseDTO = userOrderService.getUserOrderById(userOrder.getId());
        return Response.success(responseDTO);
    }

    @GetMapping("/user/{userId}")
    @ApiOperation(value = "根据用户ID查询订单列表", notes = "查询指定用户的所有订单")
    public Response<List<UserOrderResponseDTO>> getUserOrdersByUserId(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {
        
        log.info("根据用户ID查询订单列表，用户ID: {}", userId);
        var userOrders = userOrderService.getUserOrdersByUserId(userId);
        List<UserOrderResponseDTO> responseDTOList = userOrders.stream()
                .map(userOrder -> userOrderService.getUserOrderById(userOrder.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/user/{userId}/status/{orderStatus}")
    @ApiOperation(value = "根据用户ID和状态查询订单", notes = "查询指定用户指定状态的订单列表")
    public Response<List<UserOrderResponseDTO>> getUserOrdersByUserIdAndStatus(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId,
            @ApiParam(value = "订单状态", required = true, notes = "1-待支付 2-已支付 3-已取消 4-已退款")
            @PathVariable("orderStatus") @NotNull Integer orderStatus) {
        
        log.info("根据用户ID和状态查询订单，用户ID: {}, 订单状态: {}", userId, orderStatus);
        var userOrders = userOrderService.getUserOrdersByUserIdAndStatus(userId, orderStatus);
        List<UserOrderResponseDTO> responseDTOList = userOrders.stream()
                .map(userOrder -> userOrderService.getUserOrderById(userOrder.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/exists/{orderNo}")
    @ApiOperation(value = "检查订单号是否存在", notes = "检查指定订单号是否已存在")
    public Response<Boolean> existsByOrderNo(
            @ApiParam(value = "订单号", required = true)
            @PathVariable("orderNo") @NotBlank String orderNo) {
        
        log.info("检查订单号是否存在，订单号: {}", orderNo);
        boolean exists = userOrderService.existsByOrderNo(orderNo);
        return Response.success(exists);
    }

    @PostMapping("/{id}/pay")
    @ApiOperation(value = "支付订单", notes = "支付指定的订单")
    public Response<Boolean> payOrder(
            @ApiParam(value = "订单ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("支付订单，订单ID: {}", id);
        boolean result = userOrderService.payOrder(id);
        return Response.success(result);
    }

    @PostMapping("/{id}/cancel")
    @ApiOperation(value = "取消订单", notes = "取消指定的订单")
    public Response<Boolean> cancelOrder(
            @ApiParam(value = "订单ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("取消订单，订单ID: {}", id);
        boolean result = userOrderService.cancelOrder(id);
        return Response.success(result);
    }

    @PostMapping("/{id}/refund")
    @ApiOperation(value = "退款订单", notes = "退款指定的订单")
    public Response<Boolean> refundOrder(
            @ApiParam(value = "订单ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("退款订单，订单ID: {}", id);
        boolean result = userOrderService.refundOrder(id);
        return Response.success(result);
    }

    @GetMapping("/expired")
    @ApiOperation(value = "查询过期未支付订单", notes = "查询所有过期未支付的订单")
    public Response<List<UserOrderResponseDTO>> getExpiredUnpaidOrders() {
        
        log.info("查询过期未支付订单");
        var expiredOrders = userOrderService.getExpiredUnpaidOrders();
        List<UserOrderResponseDTO> responseDTOList = expiredOrders.stream()
                .map(userOrder -> userOrderService.getUserOrderById(userOrder.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @PostMapping("/auto-cancel-expired")
    @ApiOperation(value = "自动取消过期订单", notes = "自动取消所有过期未支付的订单")
    public Response<Integer> autoCancelExpiredOrders() {
        
        log.info("自动取消过期订单");
        int canceledCount = userOrderService.autoCancelExpiredOrders();
        return Response.success(canceledCount);
    }

    @GetMapping("/user/{userId}/total-amount")
    @ApiOperation(value = "查询用户总订单金额", notes = "计算指定用户的总订单金额")
    public Response<BigDecimal> getTotalOrderAmount(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {
        
        log.info("查询用户总订单金额，用户ID: {}", userId);
        BigDecimal totalAmount = userOrderService.getTotalOrderAmount(userId);
        return Response.success(totalAmount);
    }

    @GetMapping("/user/{userId}/paid-amount")
    @ApiOperation(value = "查询用户已支付订单金额", notes = "计算指定用户的已支付订单金额")
    public Response<BigDecimal> getPaidOrderAmount(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {
        
        log.info("查询用户已支付订单金额，用户ID: {}", userId);
        BigDecimal paidAmount = userOrderService.getPaidOrderAmount(userId);
        return Response.success(paidAmount);
    }

    @GetMapping("/user/{userId}/count")
    @ApiOperation(value = "统计用户订单数量", notes = "统计指定用户的订单总数量")
    public Response<Long> countUserOrders(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {
        
        log.info("统计用户订单数量，用户ID: {}", userId);
        long count = userOrderService.countUserOrders(userId);
        return Response.success(count);
    }

    @GetMapping("/user/{userId}/paid-count")
    @ApiOperation(value = "统计用户已支付订单数量", notes = "统计指定用户的已支付订单数量")
    public Response<Long> countUserPaidOrders(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {
        
        log.info("统计用户已支付订单数量，用户ID: {}", userId);
        long count = userOrderService.countUserPaidOrders(userId);
        return Response.success(count);
    }
}
