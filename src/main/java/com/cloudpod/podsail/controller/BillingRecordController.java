package com.cloudpod.podsail.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.common.base.response.Response;
import com.cloudpod.podsail.dto.billing.BillingRecordCreateDTO;
import com.cloudpod.podsail.dto.billing.BillingRecordQueryDTO;
import com.cloudpod.podsail.dto.billing.BillingRecordResponseDTO;
import com.cloudpod.podsail.dto.billing.BillingRecordUpdateDTO;
import com.cloudpod.podsail.service.BillingRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 计费记录管理控制器
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/billing-records")
@Api(tags = "计费记录管理", description = "计费记录相关的CRUD操作接口")
public class BillingRecordController {

    @Autowired
    private BillingRecordService billingRecordService;

    @PostMapping
    @ApiOperation(value = "创建计费记录", notes = "创建新的计费记录")
    public Response<BillingRecordResponseDTO> createBillingRecord(
            @ApiParam(value = "计费记录创建信息", required = true)
            @RequestBody @Valid BillingRecordCreateDTO createDTO) {
        
        log.info("创建计费记录，请求参数: {}", createDTO);
        BillingRecordResponseDTO responseDTO = billingRecordService.createBillingRecord(createDTO);
        return Response.success(responseDTO);
    }

    @PutMapping
    @ApiOperation(value = "更新计费记录", notes = "更新计费记录信息")
    public Response<BillingRecordResponseDTO> updateBillingRecord(
            @ApiParam(value = "计费记录更新信息", required = true)
            @RequestBody @Valid BillingRecordUpdateDTO updateDTO) {
        
        log.info("更新计费记录，请求参数: {}", updateDTO);
        BillingRecordResponseDTO responseDTO = billingRecordService.updateBillingRecord(updateDTO);
        return Response.success(responseDTO);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询计费记录", notes = "根据计费记录ID查询详细信息")
    public Response<BillingRecordResponseDTO> getBillingRecordById(
            @ApiParam(value = "计费记录ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("根据ID查询计费记录，ID: {}", id);
        BillingRecordResponseDTO responseDTO = billingRecordService.getBillingRecordById(id);
        return Response.success(responseDTO);
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除计费记录", notes = "根据计费记录ID删除（逻辑删除）")
    public Response<Boolean> deleteBillingRecord(
            @ApiParam(value = "计费记录ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("删除计费记录，ID: {}", id);
        boolean result = billingRecordService.deleteEntityById(id);
        return Response.success(result);
    }

    @DeleteMapping("/batch")
    @ApiOperation(value = "批量删除计费记录", notes = "根据计费记录ID列表批量删除（逻辑删除）")
    public Response<Boolean> deleteBillingRecords(
            @ApiParam(value = "计费记录ID列表", required = true)
            @RequestBody @Valid List<Long> ids) {
        
        log.info("批量删除计费记录，ID列表: {}", ids);
        boolean result = billingRecordService.deleteEntitiesByIds(ids);
        return Response.success(result);
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页查询计费记录", notes = "根据条件分页查询计费记录列表")
    public Response<IPage<BillingRecordResponseDTO>> getBillingRecordPage(
            @ApiParam(value = "查询条件", required = true)
            @RequestBody @Valid BillingRecordQueryDTO queryDTO) {
        
        log.info("分页查询计费记录，查询条件: {}", queryDTO);
        IPage<BillingRecordResponseDTO> page = billingRecordService.getBillingRecordPage(queryDTO);
        return Response.success(page);
    }

    @PostMapping("/list")
    @ApiOperation(value = "查询计费记录列表", notes = "根据条件查询计费记录列表")
    public Response<List<BillingRecordResponseDTO>> getBillingRecordList(
            @ApiParam(value = "查询条件")
            @RequestBody BillingRecordQueryDTO queryDTO) {
        
        log.info("查询计费记录列表，查询条件: {}", queryDTO);
        List<BillingRecordResponseDTO> list = billingRecordService.getBillingRecordList(queryDTO);
        return Response.success(list);
    }

    @GetMapping("/user/{userId}")
    @ApiOperation(value = "根据用户ID查询计费记录", notes = "查询指定用户的所有计费记录")
    public Response<List<BillingRecordResponseDTO>> getBillingRecordsByUserId(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {
        
        log.info("根据用户ID查询计费记录，用户ID: {}", userId);
        var billingRecords = billingRecordService.getBillingRecordsByUserId(userId);
        List<BillingRecordResponseDTO> responseDTOList = billingRecords.stream()
                .map(billingRecord -> billingRecordService.getBillingRecordById(billingRecord.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/server-instance/{serverInstanceId}")
    @ApiOperation(value = "根据服务器实例ID查询计费记录", notes = "查询指定服务器实例的所有计费记录")
    public Response<List<BillingRecordResponseDTO>> getBillingRecordsByServerInstanceId(
            @ApiParam(value = "服务器实例ID", required = true)
            @PathVariable("serverInstanceId") @NotNull Long serverInstanceId) {
        
        log.info("根据服务器实例ID查询计费记录，服务器实例ID: {}", serverInstanceId);
        var billingRecords = billingRecordService.getBillingRecordsByServerInstanceId(serverInstanceId);
        List<BillingRecordResponseDTO> responseDTOList = billingRecords.stream()
                .map(billingRecord -> billingRecordService.getBillingRecordById(billingRecord.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/user/{userId}/status/{status}")
    @ApiOperation(value = "根据用户ID和状态查询计费记录", notes = "查询指定用户指定状态的计费记录列表")
    public Response<List<BillingRecordResponseDTO>> getBillingRecordsByUserIdAndStatus(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId,
            @ApiParam(value = "计费状态", required = true, notes = "1-待扣费 2-已扣费 3-扣费失败")
            @PathVariable("status") @NotNull Integer status) {
        
        log.info("根据用户ID和状态查询计费记录，用户ID: {}, 计费状态: {}", userId, status);
        var billingRecords = billingRecordService.getBillingRecordsByUserIdAndStatus(userId, status);
        List<BillingRecordResponseDTO> responseDTOList = billingRecords.stream()
                .map(billingRecord -> billingRecordService.getBillingRecordById(billingRecord.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/server-instance/{serverInstanceId}/status/{status}")
    @ApiOperation(value = "根据服务器实例ID和状态查询计费记录", notes = "查询指定服务器实例指定状态的计费记录列表")
    public Response<List<BillingRecordResponseDTO>> getBillingRecordsByServerInstanceIdAndStatus(
            @ApiParam(value = "服务器实例ID", required = true)
            @PathVariable("serverInstanceId") @NotNull Long serverInstanceId,
            @ApiParam(value = "计费状态", required = true, notes = "1-待扣费 2-已扣费 3-扣费失败")
            @PathVariable("status") @NotNull Integer status) {
        
        log.info("根据服务器实例ID和状态查询计费记录，服务器实例ID: {}, 计费状态: {}", serverInstanceId, status);
        var billingRecords = billingRecordService.getBillingRecordsByServerInstanceIdAndStatus(serverInstanceId, status);
        List<BillingRecordResponseDTO> responseDTOList = billingRecords.stream()
                .map(billingRecord -> billingRecordService.getBillingRecordById(billingRecord.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/pending")
    @ApiOperation(value = "查询待扣费的计费记录", notes = "查询所有待扣费的计费记录")
    public Response<List<BillingRecordResponseDTO>> getPendingBillingRecords() {
        
        log.info("查询待扣费的计费记录");
        var pendingRecords = billingRecordService.getPendingBillingRecords();
        List<BillingRecordResponseDTO> responseDTOList = pendingRecords.stream()
                .map(billingRecord -> billingRecordService.getBillingRecordById(billingRecord.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/failed")
    @ApiOperation(value = "查询扣费失败的计费记录", notes = "查询所有扣费失败的计费记录")
    public Response<List<BillingRecordResponseDTO>> getFailedBillingRecords() {
        
        log.info("查询扣费失败的计费记录");
        var failedRecords = billingRecordService.getFailedBillingRecords();
        List<BillingRecordResponseDTO> responseDTOList = failedRecords.stream()
                .map(billingRecord -> billingRecordService.getBillingRecordById(billingRecord.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @PostMapping("/{id}/charge-success")
    @ApiOperation(value = "标记扣费成功", notes = "标记指定计费记录为扣费成功")
    public Response<Boolean> chargeSuccess(
            @ApiParam(value = "计费记录ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("标记扣费成功，计费记录ID: {}", id);
        boolean result = billingRecordService.chargeSuccess(id);
        return Response.success(result);
    }

    @PostMapping("/{id}/charge-failed")
    @ApiOperation(value = "标记扣费失败", notes = "标记指定计费记录为扣费失败")
    public Response<Boolean> chargeFailed(
            @ApiParam(value = "计费记录ID", required = true)
            @PathVariable("id") @NotNull Long id,
            @ApiParam(value = "失败原因")
            @RequestParam(value = "remark", required = false) String remark) {
        
        log.info("标记扣费失败，计费记录ID: {}, 失败原因: {}", id, remark);
        boolean result = billingRecordService.chargeFailed(id, remark);
        return Response.success(result);
    }

    @PostMapping("/batch-charge-success")
    @ApiOperation(value = "批量标记扣费成功", notes = "批量标记计费记录为扣费成功")
    public Response<Integer> batchChargeSuccess(
            @ApiParam(value = "计费记录ID列表", required = true)
            @RequestBody @Valid List<Long> ids) {
        
        log.info("批量标记扣费成功，计费记录ID列表: {}", ids);
        int successCount = billingRecordService.batchChargeSuccess(ids);
        return Response.success(successCount);
    }

    @GetMapping("/user/{userId}/total-amount")
    @ApiOperation(value = "查询用户总计费金额", notes = "计算指定用户的总计费金额")
    public Response<BigDecimal> getTotalBillingAmount(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {
        
        log.info("查询用户总计费金额，用户ID: {}", userId);
        BigDecimal totalAmount = billingRecordService.getTotalBillingAmount(userId);
        return Response.success(totalAmount);
    }

    @GetMapping("/user/{userId}/charged-amount")
    @ApiOperation(value = "查询用户已扣费金额", notes = "计算指定用户的已扣费金额")
    public Response<BigDecimal> getChargedAmount(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {
        
        log.info("查询用户已扣费金额，用户ID: {}", userId);
        BigDecimal chargedAmount = billingRecordService.getChargedAmount(userId);
        return Response.success(chargedAmount);
    }

    @GetMapping("/user/{userId}/pending-amount")
    @ApiOperation(value = "查询用户待扣费金额", notes = "计算指定用户的待扣费金额")
    public Response<BigDecimal> getPendingAmount(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {
        
        log.info("查询用户待扣费金额，用户ID: {}", userId);
        BigDecimal pendingAmount = billingRecordService.getPendingAmount(userId);
        return Response.success(pendingAmount);
    }

    @GetMapping("/server-instance/{serverInstanceId}/total-amount")
    @ApiOperation(value = "查询服务器实例总计费金额", notes = "计算指定服务器实例的总计费金额")
    public Response<BigDecimal> getServerInstanceTotalBillingAmount(
            @ApiParam(value = "服务器实例ID", required = true)
            @PathVariable("serverInstanceId") @NotNull Long serverInstanceId) {
        
        log.info("查询服务器实例总计费金额，服务器实例ID: {}", serverInstanceId);
        BigDecimal totalAmount = billingRecordService.getServerInstanceTotalBillingAmount(serverInstanceId);
        return Response.success(totalAmount);
    }

    @GetMapping("/user/{userId}/count")
    @ApiOperation(value = "统计用户计费记录数量", notes = "统计指定用户的计费记录总数量")
    public Response<Long> countUserBillingRecords(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {
        
        log.info("统计用户计费记录数量，用户ID: {}", userId);
        long count = billingRecordService.countUserBillingRecords(userId);
        return Response.success(count);
    }

    @GetMapping("/user/{userId}/charged-count")
    @ApiOperation(value = "统计用户已扣费记录数量", notes = "统计指定用户的已扣费记录数量")
    public Response<Long> countUserChargedRecords(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {
        
        log.info("统计用户已扣费记录数量，用户ID: {}", userId);
        long count = billingRecordService.countUserChargedRecords(userId);
        return Response.success(count);
    }

    @GetMapping("/user/{userId}/pending-count")
    @ApiOperation(value = "统计用户待扣费记录数量", notes = "统计指定用户的待扣费记录数量")
    public Response<Long> countUserPendingRecords(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable("userId") @NotNull Long userId) {
        
        log.info("统计用户待扣费记录数量，用户ID: {}", userId);
        long count = billingRecordService.countUserPendingRecords(userId);
        return Response.success(count);
    }
}
