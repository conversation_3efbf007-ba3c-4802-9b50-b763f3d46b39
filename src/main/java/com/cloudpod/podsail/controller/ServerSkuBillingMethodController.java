package com.cloudpod.podsail.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.common.base.response.Response;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodCreateDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodQueryDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodResponseDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodUpdateDTO;
import com.cloudpod.podsail.service.ServerSkuBillingMethodService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 主机套餐规格定价管理控制器
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/server-sku-billing-methods")
@Api(tags = "主机套餐规格定价管理", description = "主机套餐规格定价相关的CRUD操作接口")
public class ServerSkuBillingMethodController {

    @Autowired
    private ServerSkuBillingMethodService serverSkuBillingMethodService;

    @PostMapping
    @ApiOperation(value = "创建主机套餐规格定价", notes = "创建新的主机套餐规格定价")
    public Response<ServerSkuBillingMethodResponseDTO> createServerSkuBillingMethod(
            @ApiParam(value = "主机套餐规格定价创建信息", required = true)
            @RequestBody @Valid ServerSkuBillingMethodCreateDTO createDTO) {
        
        log.info("创建主机套餐规格定价，请求参数: {}", createDTO);
        ServerSkuBillingMethodResponseDTO responseDTO = serverSkuBillingMethodService.createServerSkuBillingMethod(createDTO);
        return Response.success(responseDTO);
    }

    @PutMapping
    @ApiOperation(value = "更新主机套餐规格定价", notes = "更新主机套餐规格定价信息")
    public Response<ServerSkuBillingMethodResponseDTO> updateServerSkuBillingMethod(
            @ApiParam(value = "主机套餐规格定价更新信息", required = true)
            @RequestBody @Valid ServerSkuBillingMethodUpdateDTO updateDTO) {
        
        log.info("更新主机套餐规格定价，请求参数: {}", updateDTO);
        ServerSkuBillingMethodResponseDTO responseDTO = serverSkuBillingMethodService.updateServerSkuBillingMethod(updateDTO);
        return Response.success(responseDTO);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询主机套餐规格定价", notes = "根据主机套餐规格定价ID查询详细信息")
    public Response<ServerSkuBillingMethodResponseDTO> getServerSkuBillingMethodById(
            @ApiParam(value = "主机套餐规格定价ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("根据ID查询主机套餐规格定价，ID: {}", id);
        ServerSkuBillingMethodResponseDTO responseDTO = serverSkuBillingMethodService.getServerSkuBillingMethodById(id);
        return Response.success(responseDTO);
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除主机套餐规格定价", notes = "根据主机套餐规格定价ID删除（逻辑删除）")
    public Response<Boolean> deleteServerSkuBillingMethod(
            @ApiParam(value = "主机套餐规格定价ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("删除主机套餐规格定价，ID: {}", id);
        boolean result = serverSkuBillingMethodService.deleteEntityById(id);
        return Response.success(result);
    }

    @DeleteMapping("/batch")
    @ApiOperation(value = "批量删除主机套餐规格定价", notes = "根据主机套餐规格定价ID列表批量删除（逻辑删除）")
    public Response<Boolean> deleteServerSkuBillingMethods(
            @ApiParam(value = "主机套餐规格定价ID列表", required = true)
            @RequestBody @Valid List<Long> ids) {
        
        log.info("批量删除主机套餐规格定价，ID列表: {}", ids);
        boolean result = serverSkuBillingMethodService.deleteEntitiesByIds(ids);
        return Response.success(result);
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页查询主机套餐规格定价", notes = "根据条件分页查询主机套餐规格定价列表")
    public Response<IPage<ServerSkuBillingMethodResponseDTO>> getServerSkuBillingMethodPage(
            @ApiParam(value = "查询条件", required = true)
            @RequestBody @Valid ServerSkuBillingMethodQueryDTO queryDTO) {
        
        log.info("分页查询主机套餐规格定价，查询条件: {}", queryDTO);
        IPage<ServerSkuBillingMethodResponseDTO> page = serverSkuBillingMethodService.getServerSkuBillingMethodPage(queryDTO);
        return Response.success(page);
    }

    @PostMapping("/list")
    @ApiOperation(value = "查询主机套餐规格定价列表", notes = "根据条件查询主机套餐规格定价列表")
    public Response<List<ServerSkuBillingMethodResponseDTO>> getServerSkuBillingMethodList(
            @ApiParam(value = "查询条件")
            @RequestBody ServerSkuBillingMethodQueryDTO queryDTO) {
        
        log.info("查询主机套餐规格定价列表，查询条件: {}", queryDTO);
        List<ServerSkuBillingMethodResponseDTO> list = serverSkuBillingMethodService.getServerSkuBillingMethodList(queryDTO);
        return Response.success(list);
    }

    @GetMapping("/server-sku/{serverSkuId}")
    @ApiOperation(value = "根据服务器套餐SKU ID查询定价", notes = "查询指定服务器套餐SKU的所有定价")
    public Response<List<ServerSkuBillingMethodResponseDTO>> getServerSkuBillingMethodsByServerSkuId(
            @ApiParam(value = "服务器套餐SKU ID", required = true)
            @PathVariable("serverSkuId") @NotBlank String serverSkuId) {
        
        log.info("根据服务器套餐SKU ID查询定价，服务器套餐SKU ID: {}", serverSkuId);
        var serverSkuBillingMethods = serverSkuBillingMethodService.getServerSkuBillingMethodsByServerSkuId(serverSkuId);
        List<ServerSkuBillingMethodResponseDTO> responseDTOList = serverSkuBillingMethods.stream()
                .map(serverSkuBillingMethod -> serverSkuBillingMethodService.getServerSkuBillingMethodById(serverSkuBillingMethod.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/server-sku/{serverSkuId}/billing-type/{billingType}")
    @ApiOperation(value = "根据服务器套餐SKU ID和计费类型查询定价", notes = "查询指定服务器套餐SKU和计费类型的定价列表")
    public Response<List<ServerSkuBillingMethodResponseDTO>> getServerSkuBillingMethodsByServerSkuIdAndBillingType(
            @ApiParam(value = "服务器套餐SKU ID", required = true)
            @PathVariable("serverSkuId") @NotBlank String serverSkuId,
            @ApiParam(value = "计费类型", required = true, notes = "1-按需付费 2-包年包月")
            @PathVariable("billingType") @NotNull Integer billingType) {
        
        log.info("根据服务器套餐SKU ID和计费类型查询定价，服务器套餐SKU ID: {}, 计费类型: {}", serverSkuId, billingType);
        var serverSkuBillingMethods = serverSkuBillingMethodService.getServerSkuBillingMethodsByServerSkuIdAndBillingType(serverSkuId, billingType);
        List<ServerSkuBillingMethodResponseDTO> responseDTOList = serverSkuBillingMethods.stream()
                .map(serverSkuBillingMethod -> serverSkuBillingMethodService.getServerSkuBillingMethodById(serverSkuBillingMethod.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/server-sku/{serverSkuId}/billing-type/{billingType}/billing-cycle/{billingCycle}")
    @ApiOperation(value = "根据服务器套餐SKU ID、计费类型和计费周期查询定价", notes = "查询指定条件的定价信息")
    public Response<ServerSkuBillingMethodResponseDTO> getServerSkuBillingMethodBySkuAndTypeAndCycle(
            @ApiParam(value = "服务器套餐SKU ID", required = true)
            @PathVariable("serverSkuId") @NotBlank String serverSkuId,
            @ApiParam(value = "计费类型", required = true, notes = "1-按需付费 2-包年包月")
            @PathVariable("billingType") @NotNull Integer billingType,
            @ApiParam(value = "计费周期", required = true)
            @PathVariable("billingCycle") @NotBlank String billingCycle) {
        
        log.info("根据服务器套餐SKU ID、计费类型和计费周期查询定价，服务器套餐SKU ID: {}, 计费类型: {}, 计费周期: {}", 
                serverSkuId, billingType, billingCycle);
        var serverSkuBillingMethod = serverSkuBillingMethodService.getServerSkuBillingMethodBySkuAndTypeAndCycle(serverSkuId, billingType, billingCycle);
        if (serverSkuBillingMethod == null) {
            return Response.success(null);
        }
        ServerSkuBillingMethodResponseDTO responseDTO = serverSkuBillingMethodService.getServerSkuBillingMethodById(serverSkuBillingMethod.getId());
        return Response.success(responseDTO);
    }

    @GetMapping("/region/{regionId}")
    @ApiOperation(value = "根据区域ID查询定价", notes = "查询指定区域的所有定价")
    public Response<List<ServerSkuBillingMethodResponseDTO>> getServerSkuBillingMethodsByRegionId(
            @ApiParam(value = "区域ID", required = true)
            @PathVariable("regionId") @NotBlank String regionId) {
        
        log.info("根据区域ID查询定价，区域ID: {}", regionId);
        var serverSkuBillingMethods = serverSkuBillingMethodService.getServerSkuBillingMethodsByRegionId(regionId);
        List<ServerSkuBillingMethodResponseDTO> responseDTOList = serverSkuBillingMethods.stream()
                .map(serverSkuBillingMethod -> serverSkuBillingMethodService.getServerSkuBillingMethodById(serverSkuBillingMethod.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/region/{regionId}/zone/{zoneId}")
    @ApiOperation(value = "根据区域ID和可用区ID查询定价", notes = "查询指定区域和可用区的定价列表")
    public Response<List<ServerSkuBillingMethodResponseDTO>> getServerSkuBillingMethodsByRegionIdAndZoneId(
            @ApiParam(value = "区域ID", required = true)
            @PathVariable("regionId") @NotBlank String regionId,
            @ApiParam(value = "可用区ID", required = true)
            @PathVariable("zoneId") @NotBlank String zoneId) {
        
        log.info("根据区域ID和可用区ID查询定价，区域ID: {}, 可用区ID: {}", regionId, zoneId);
        var serverSkuBillingMethods = serverSkuBillingMethodService.getServerSkuBillingMethodsByRegionIdAndZoneId(regionId, zoneId);
        List<ServerSkuBillingMethodResponseDTO> responseDTOList = serverSkuBillingMethods.stream()
                .map(serverSkuBillingMethod -> serverSkuBillingMethodService.getServerSkuBillingMethodById(serverSkuBillingMethod.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/effective")
    @ApiOperation(value = "查询有效的定价", notes = "查询所有有效的主机套餐规格定价")
    public Response<List<ServerSkuBillingMethodResponseDTO>> getEffectiveServerSkuBillingMethods() {
        
        log.info("查询有效的定价");
        var effectiveBillingMethods = serverSkuBillingMethodService.getEffectiveServerSkuBillingMethods();
        List<ServerSkuBillingMethodResponseDTO> responseDTOList = effectiveBillingMethods.stream()
                .map(serverSkuBillingMethod -> serverSkuBillingMethodService.getServerSkuBillingMethodById(serverSkuBillingMethod.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @GetMapping("/expired")
    @ApiOperation(value = "查询过期的定价", notes = "查询所有过期的主机套餐规格定价")
    public Response<List<ServerSkuBillingMethodResponseDTO>> getExpiredServerSkuBillingMethods() {
        
        log.info("查询过期的定价");
        var expiredBillingMethods = serverSkuBillingMethodService.getExpiredServerSkuBillingMethods();
        List<ServerSkuBillingMethodResponseDTO> responseDTOList = expiredBillingMethods.stream()
                .map(serverSkuBillingMethod -> serverSkuBillingMethodService.getServerSkuBillingMethodById(serverSkuBillingMethod.getId()))
                .toList();
        return Response.success(responseDTOList);
    }

    @PostMapping("/{id}/enable")
    @ApiOperation(value = "启用定价", notes = "启用指定的主机套餐规格定价")
    public Response<Boolean> enableServerSkuBillingMethod(
            @ApiParam(value = "主机套餐规格定价ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("启用定价，ID: {}", id);
        boolean result = serverSkuBillingMethodService.enableServerSkuBillingMethod(id);
        return Response.success(result);
    }

    @PostMapping("/{id}/disable")
    @ApiOperation(value = "禁用定价", notes = "禁用指定的主机套餐规格定价")
    public Response<Boolean> disableServerSkuBillingMethod(
            @ApiParam(value = "主机套餐规格定价ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("禁用定价，ID: {}", id);
        boolean result = serverSkuBillingMethodService.disableServerSkuBillingMethod(id);
        return Response.success(result);
    }

    @PostMapping("/auto-disable-expired")
    @ApiOperation(value = "自动禁用过期定价", notes = "自动禁用所有过期的主机套餐规格定价")
    public Response<Integer> autoDisableExpiredBillingMethods() {
        
        log.info("自动禁用过期定价");
        int disabledCount = serverSkuBillingMethodService.autoDisableExpiredBillingMethods();
        return Response.success(disabledCount);
    }

    @GetMapping("/{id}/effective")
    @ApiOperation(value = "检查定价是否有效", notes = "检查指定的主机套餐规格定价是否有效")
    public Response<Boolean> isServerSkuBillingMethodEffective(
            @ApiParam(value = "主机套餐规格定价ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("检查定价是否有效，ID: {}", id);
        boolean effective = serverSkuBillingMethodService.isServerSkuBillingMethodEffective(id);
        return Response.success(effective);
    }

    @GetMapping("/exists")
    @ApiOperation(value = "检查定价组合是否存在", notes = "检查指定的定价组合是否已存在")
    public Response<Boolean> existsBySkuAndTypeAndCycleAndRegionAndZone(
            @ApiParam(value = "服务器套餐SKU ID", required = true) @RequestParam("serverSkuId") @NotBlank String serverSkuId,
            @ApiParam(value = "计费类型", required = true) @RequestParam("billingType") @NotNull Integer billingType,
            @ApiParam(value = "计费周期", required = true) @RequestParam("billingCycle") @NotBlank String billingCycle,
            @ApiParam(value = "区域ID") @RequestParam(value = "regionId", required = false) String regionId,
            @ApiParam(value = "可用区ID") @RequestParam(value = "zoneId", required = false) String zoneId) {
        
        log.info("检查定价组合是否存在，服务器套餐SKU ID: {}, 计费类型: {}, 计费周期: {}, 区域ID: {}, 可用区ID: {}", 
                serverSkuId, billingType, billingCycle, regionId, zoneId);
        boolean exists = serverSkuBillingMethodService.existsBySkuAndTypeAndCycleAndRegionAndZone(
                serverSkuId, billingType, billingCycle, regionId, zoneId);
        return Response.success(exists);
    }
}
