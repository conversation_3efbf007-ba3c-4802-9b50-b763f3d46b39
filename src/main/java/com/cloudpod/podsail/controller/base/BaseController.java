package com.cloudpod.podsail.controller.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.common.base.dto.BasePageQuery;
import com.cloudpod.podsail.common.base.dto.RequestByID;
import com.cloudpod.podsail.common.base.response.Response;
import com.cloudpod.podsail.db.base.Entity;
import com.cloudpod.podsail.service.base.BaseService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 基础Controller类
 * 提供通用的CRUD接口
 *
 * @param <S> Service类型
 * @param <T> 实体类型
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Validated
public abstract class BaseController<S extends BaseService<T>, T extends Entity<?>> {

    @Autowired
    protected S baseService;

    /**
     * 根据ID查询实体
     *
     * @param id 主键ID
     * @return 实体对象
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询", notes = "根据主键ID查询单个实体")
    public Response<T> getById(
            @ApiParam(value = "主键ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("根据ID查询实体，ID: {}", id);
        T entity = baseService.getEntityById(id);
        return Response.success(entity);
    }

    /**
     * 创建实体
     *
     * @param entity 实体对象
     * @return 创建后的实体对象
     */
    @PostMapping
    @ApiOperation(value = "创建实体", notes = "创建新的实体")
    public Response<T> create(
            @ApiParam(value = "实体对象", required = true)
            @RequestBody @Valid T entity) {
        
        log.info("创建实体，实体: {}", entity);
        T createdEntity = baseService.createEntity(entity);
        return Response.success(createdEntity);
    }

    /**
     * 更新实体
     *
     * @param entity 实体对象
     * @return 更新后的实体对象
     */
    @PutMapping
    @ApiOperation(value = "更新实体", notes = "更新现有实体")
    public Response<T> update(
            @ApiParam(value = "实体对象", required = true)
            @RequestBody @Valid T entity) {
        
        log.info("更新实体，实体: {}", entity);
        T updatedEntity = baseService.updateEntity(entity);
        return Response.success(updatedEntity);
    }

    /**
     * 根据ID删除实体
     *
     * @param id 主键ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "根据ID删除", notes = "根据主键ID删除实体（逻辑删除）")
    public Response<Boolean> deleteById(
            @ApiParam(value = "主键ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("根据ID删除实体，ID: {}", id);
        boolean result = baseService.deleteEntityById(id);
        return Response.success(result);
    }

    /**
     * 批量删除实体
     *
     * @param ids 主键ID列表
     * @return 删除结果
     */
    @DeleteMapping("/batch")
    @ApiOperation(value = "批量删除", notes = "根据主键ID列表批量删除实体（逻辑删除）")
    public Response<Boolean> deleteByIds(
            @ApiParam(value = "主键ID列表", required = true)
            @RequestBody @Valid List<Long> ids) {
        
        log.info("批量删除实体，ID列表: {}", ids);
        boolean result = baseService.deleteEntitiesByIds(ids);
        return Response.success(result);
    }

    /**
     * 分页查询实体列表
     *
     * @param pageQuery 分页查询参数
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询", notes = "分页查询实体列表")
    public Response<IPage<T>> getPage(
            @ApiParam(value = "分页查询参数", required = true)
            @RequestBody @Valid BasePageQuery pageQuery) {
        
        log.info("分页查询实体，分页参数: {}", pageQuery);
        IPage<T> page = baseService.getEntityPage(pageQuery);
        return Response.success(page);
    }

    /**
     * 查询所有实体列表
     *
     * @return 实体列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询所有", notes = "查询所有实体列表")
    public Response<List<T>> getList() {
        
        log.info("查询所有实体");
        List<T> list = baseService.getAllEntities();
        return Response.success(list);
    }

    /**
     * 根据条件查询实体列表
     *
     * @param entity 查询条件实体
     * @return 实体列表
     */
    @PostMapping("/list/condition")
    @ApiOperation(value = "条件查询", notes = "根据条件查询实体列表")
    public Response<List<T>> getListByCondition(
            @ApiParam(value = "查询条件实体")
            @RequestBody T entity) {
        
        log.info("根据条件查询实体，条件: {}", entity);
        List<T> list = baseService.getEntitiesByCondition(entity);
        return Response.success(list);
    }

    /**
     * 统计实体数量
     *
     * @return 实体数量
     */
    @GetMapping("/count")
    @ApiOperation(value = "统计数量", notes = "统计实体总数量")
    public Response<Long> count() {
        
        log.info("统计实体数量");
        long count = baseService.countEntities();
        return Response.success(count);
    }

    /**
     * 根据条件统计实体数量
     *
     * @param entity 查询条件实体
     * @return 实体数量
     */
    @PostMapping("/count/condition")
    @ApiOperation(value = "条件统计", notes = "根据条件统计实体数量")
    public Response<Long> countByCondition(
            @ApiParam(value = "查询条件实体")
            @RequestBody T entity) {
        
        log.info("根据条件统计实体数量，条件: {}", entity);
        long count = baseService.countEntitiesByCondition(entity);
        return Response.success(count);
    }
}
