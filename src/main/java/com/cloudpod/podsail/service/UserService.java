package com.cloudpod.podsail.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.db.entity.User;
import com.cloudpod.podsail.dto.user.UserCreateDTO;
import com.cloudpod.podsail.dto.user.UserQueryDTO;
import com.cloudpod.podsail.dto.user.UserResponseDTO;
import com.cloudpod.podsail.dto.user.UserUpdateDTO;
import com.cloudpod.podsail.service.base.BaseService;

import java.util.List;

/**
 * 用户服务接口
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
public interface UserService extends BaseService<User> {

    /**
     * 创建用户
     *
     * @param createDTO 创建用户DTO
     * @return 用户响应DTO
     */
    UserResponseDTO createUser(UserCreateDTO createDTO);

    /**
     * 更新用户
     *
     * @param updateDTO 更新用户DTO
     * @return 用户响应DTO
     */
    UserResponseDTO updateUser(UserUpdateDTO updateDTO);

    /**
     * 根据ID获取用户
     *
     * @param id 用户ID
     * @return 用户响应DTO
     */
    UserResponseDTO getUserById(Long id);

    /**
     * 分页查询用户
     *
     * @param queryDTO 查询条件DTO
     * @return 分页结果
     */
    IPage<UserResponseDTO> getUserPage(UserQueryDTO queryDTO);

    /**
     * 根据条件查询用户列表
     *
     * @param queryDTO 查询条件DTO
     * @return 用户响应DTO列表
     */
    List<UserResponseDTO> getUserList(UserQueryDTO queryDTO);

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户实体
     */
    User getUserByUsername(String username);

    /**
     * 根据用户唯一码查询用户
     *
     * @param code 用户唯一码
     * @return 用户实体
     */
    User getUserByCode(String code);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查用户唯一码是否存在
     *
     * @param code 用户唯一码
     * @return 是否存在
     */
    boolean existsByCode(String code);

    /**
     * 验证用户密码
     *
     * @param username 用户名
     * @param password 密码
     * @return 是否验证成功
     */
    boolean validatePassword(String username, String password);

    /**
     * 更新用户密码
     *
     * @param userId      用户ID
     * @param newPassword 新密码
     * @return 是否更新成功
     */
    boolean updatePassword(Long userId, String newPassword);

    /**
     * 更新用户余额
     *
     * @param userId 用户ID
     * @param amount 金额（正数为增加，负数为减少）
     * @return 是否更新成功
     */
    boolean updateBalance(Long userId, java.math.BigDecimal amount);
}
