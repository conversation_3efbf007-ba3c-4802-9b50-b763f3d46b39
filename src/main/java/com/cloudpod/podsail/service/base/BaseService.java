package com.cloudpod.podsail.service.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudpod.podsail.common.base.dto.BasePageQuery;
import com.cloudpod.podsail.db.base.Entity;

import java.util.List;

/**
 * 基础Service接口
 * 提供通用的CRUD操作
 *
 * @param <T> 实体类型
 * <AUTHOR>
 * @since 2025-08-19
 */
public interface BaseService<T extends Entity<?>> extends IService<T> {

    /**
     * 根据ID查询实体
     *
     * @param id 主键ID
     * @return 实体对象
     */
    T getEntityById(Long id);

    /**
     * 创建实体
     *
     * @param entity 实体对象
     * @return 创建后的实体对象
     */
    T createEntity(T entity);

    /**
     * 更新实体
     *
     * @param entity 实体对象
     * @return 更新后的实体对象
     */
    T updateEntity(T entity);

    /**
     * 根据ID删除实体（逻辑删除）
     *
     * @param id 主键ID
     * @return 是否删除成功
     */
    boolean deleteEntityById(Long id);

    /**
     * 批量删除实体（逻辑删除）
     *
     * @param ids 主键ID列表
     * @return 是否删除成功
     */
    boolean deleteEntitiesByIds(List<Long> ids);

    /**
     * 分页查询实体列表
     *
     * @param pageQuery 分页查询参数
     * @return 分页结果
     */
    IPage<T> getEntityPage(BasePageQuery pageQuery);

    /**
     * 查询所有实体列表
     *
     * @return 实体列表
     */
    List<T> getAllEntities();

    /**
     * 根据条件查询实体列表
     *
     * @param entity 查询条件实体
     * @return 实体列表
     */
    List<T> getEntitiesByCondition(T entity);

    /**
     * 根据条件查询单个实体
     *
     * @param entity 查询条件实体
     * @return 实体对象
     */
    T getEntityByCondition(T entity);

    /**
     * 统计实体数量
     *
     * @return 实体数量
     */
    long countEntities();

    /**
     * 根据条件统计实体数量
     *
     * @param entity 查询条件实体
     * @return 实体数量
     */
    long countEntitiesByCondition(T entity);
}
