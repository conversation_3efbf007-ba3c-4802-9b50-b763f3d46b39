package com.cloudpod.podsail.service.base;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudpod.podsail.common.base.dto.BasePageQuery;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.db.base.Entity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 基础Service实现类
 * 提供通用的CRUD操作实现
 *
 * @param <M> Mapper类型
 * @param <T> 实体类型
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
public abstract class BaseServiceImpl<M extends BaseMapper<T>, T extends Entity<?>> 
        extends ServiceImpl<M, T> implements BaseService<T> {

    @Override
    public T getEntityById(Long id) {
        if (id == null || id <= 0) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "ID不能为空或小于等于0");
        }
        T entity = getById(id);
        if (entity == null) {
            throw new PodSailException(PodSailErrorCodeEnum.RESOURCE_NOT_FOUND, "数据不存在");
        }
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public T createEntity(T entity) {
        if (entity == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "实体对象不能为空");
        }
        
        // 验证实体数据
        validateEntityForCreate(entity);
        
        boolean success = save(entity);
        if (!success) {
            throw new PodSailException(PodSailErrorCodeEnum.SYSTEM_ERROR, "创建失败");
        }
        
        log.info("创建实体成功，ID: {}", entity.getId());
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public T updateEntity(T entity) {
        if (entity == null || entity.getId() == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "实体对象或ID不能为空");
        }
        
        // 检查实体是否存在
        T existingEntity = getById(entity.getId());
        if (existingEntity == null) {
            throw new PodSailException(PodSailErrorCodeEnum.RESOURCE_NOT_FOUND, "数据不存在");
        }
        
        // 验证实体数据
        validateEntityForUpdate(entity);
        
        boolean success = updateById(entity);
        if (!success) {
            throw new PodSailException(PodSailErrorCodeEnum.SYSTEM_ERROR, "更新失败");
        }
        
        log.info("更新实体成功，ID: {}", entity.getId());
        return getById(entity.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteEntityById(Long id) {
        if (id == null || id <= 0) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "ID不能为空或小于等于0");
        }
        
        // 检查实体是否存在
        T existingEntity = getById(id);
        if (existingEntity == null) {
            throw new PodSailException(PodSailErrorCodeEnum.RESOURCE_NOT_FOUND, "数据不存在");
        }
        
        boolean success = removeById(id);
        if (!success) {
            throw new PodSailException(PodSailErrorCodeEnum.SYSTEM_ERROR, "删除失败");
        }
        
        log.info("删除实体成功，ID: {}", id);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteEntitiesByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "ID列表不能为空");
        }
        
        boolean success = removeByIds(ids);
        if (!success) {
            throw new PodSailException(PodSailErrorCodeEnum.SYSTEM_ERROR, "批量删除失败");
        }
        
        log.info("批量删除实体成功，数量: {}", ids.size());
        return true;
    }

    @Override
    public IPage<T> getEntityPage(BasePageQuery pageQuery) {
        if (pageQuery == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "分页参数不能为空");
        }
        
        IPage<T> page = pageQuery.page();
        QueryWrapper<T> queryWrapper = buildPageQueryWrapper(pageQuery);
        
        return page(page, queryWrapper);
    }

    @Override
    public List<T> getAllEntities() {
        return list();
    }

    @Override
    public List<T> getEntitiesByCondition(T entity) {
        if (entity == null) {
            return getAllEntities();
        }
        
        QueryWrapper<T> queryWrapper = buildConditionQueryWrapper(entity);
        return list(queryWrapper);
    }

    @Override
    public T getEntityByCondition(T entity) {
        if (entity == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "查询条件不能为空");
        }
        
        QueryWrapper<T> queryWrapper = buildConditionQueryWrapper(entity);
        return getOne(queryWrapper);
    }

    @Override
    public long countEntities() {
        return count();
    }

    @Override
    public long countEntitiesByCondition(T entity) {
        if (entity == null) {
            return countEntities();
        }
        
        QueryWrapper<T> queryWrapper = buildConditionQueryWrapper(entity);
        return count(queryWrapper);
    }

    /**
     * 构建分页查询条件
     * 子类可以重写此方法来自定义查询条件
     *
     * @param pageQuery 分页查询参数
     * @return 查询条件包装器
     */
    protected QueryWrapper<T> buildPageQueryWrapper(BasePageQuery pageQuery) {
        return new QueryWrapper<>();
    }

    /**
     * 构建条件查询包装器
     * 子类可以重写此方法来自定义查询条件
     *
     * @param entity 查询条件实体
     * @return 查询条件包装器
     */
    protected QueryWrapper<T> buildConditionQueryWrapper(T entity) {
        return new QueryWrapper<>(entity);
    }

    /**
     * 创建实体前的验证
     * 子类可以重写此方法来添加自定义验证逻辑
     *
     * @param entity 待创建的实体
     */
    protected void validateEntityForCreate(T entity) {
        // 默认不做验证，子类可以重写
    }

    /**
     * 更新实体前的验证
     * 子类可以重写此方法来添加自定义验证逻辑
     *
     * @param entity 待更新的实体
     */
    protected void validateEntityForUpdate(T entity) {
        // 默认不做验证，子类可以重写
    }
}
