package com.cloudpod.podsail.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.db.entity.BillingRecord;
import com.cloudpod.podsail.dto.billing.BillingRecordCreateDTO;
import com.cloudpod.podsail.dto.billing.BillingRecordQueryDTO;
import com.cloudpod.podsail.dto.billing.BillingRecordResponseDTO;
import com.cloudpod.podsail.dto.billing.BillingRecordUpdateDTO;
import com.cloudpod.podsail.service.base.BaseService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 计费记录服务接口
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
public interface BillingRecordService extends BaseService<BillingRecord> {

    /**
     * 创建计费记录
     *
     * @param createDTO 创建计费记录DTO
     * @return 计费记录响应DTO
     */
    BillingRecordResponseDTO createBillingRecord(BillingRecordCreateDTO createDTO);

    /**
     * 更新计费记录
     *
     * @param updateDTO 更新计费记录DTO
     * @return 计费记录响应DTO
     */
    BillingRecordResponseDTO updateBillingRecord(BillingRecordUpdateDTO updateDTO);

    /**
     * 根据ID获取计费记录
     *
     * @param id 计费记录ID
     * @return 计费记录响应DTO
     */
    BillingRecordResponseDTO getBillingRecordById(Long id);

    /**
     * 分页查询计费记录
     *
     * @param queryDTO 查询条件DTO
     * @return 分页结果
     */
    IPage<BillingRecordResponseDTO> getBillingRecordPage(BillingRecordQueryDTO queryDTO);

    /**
     * 根据条件查询计费记录列表
     *
     * @param queryDTO 查询条件DTO
     * @return 计费记录响应DTO列表
     */
    List<BillingRecordResponseDTO> getBillingRecordList(BillingRecordQueryDTO queryDTO);

    /**
     * 根据用户ID查询计费记录列表
     *
     * @param userId 用户ID
     * @return 计费记录实体列表
     */
    List<BillingRecord> getBillingRecordsByUserId(Long userId);

    /**
     * 根据服务器实例ID查询计费记录列表
     *
     * @param serverInstanceId 服务器实例ID
     * @return 计费记录实体列表
     */
    List<BillingRecord> getBillingRecordsByServerInstanceId(Long serverInstanceId);

    /**
     * 根据用户ID和计费状态查询计费记录列表
     *
     * @param userId 用户ID
     * @param status 计费状态
     * @return 计费记录实体列表
     */
    List<BillingRecord> getBillingRecordsByUserIdAndStatus(Long userId, Integer status);

    /**
     * 根据服务器实例ID和计费状态查询计费记录列表
     *
     * @param serverInstanceId 服务器实例ID
     * @param status           计费状态
     * @return 计费记录实体列表
     */
    List<BillingRecord> getBillingRecordsByServerInstanceIdAndStatus(Long serverInstanceId, Integer status);

    /**
     * 查询待扣费的计费记录
     *
     * @return 待扣费的计费记录列表
     */
    List<BillingRecord> getPendingBillingRecords();

    /**
     * 查询扣费失败的计费记录
     *
     * @return 扣费失败的计费记录列表
     */
    List<BillingRecord> getFailedBillingRecords();

    /**
     * 扣费成功
     *
     * @param id 计费记录ID
     * @return 是否扣费成功
     */
    boolean chargeSuccess(Long id);

    /**
     * 扣费失败
     *
     * @param id     计费记录ID
     * @param remark 失败原因
     * @return 是否更新成功
     */
    boolean chargeFailed(Long id, String remark);

    /**
     * 批量扣费成功
     *
     * @param ids 计费记录ID列表
     * @return 成功扣费的数量
     */
    int batchChargeSuccess(List<Long> ids);

    /**
     * 计算用户总计费金额
     *
     * @param userId 用户ID
     * @return 总计费金额
     */
    BigDecimal getTotalBillingAmount(Long userId);

    /**
     * 计算用户已扣费金额
     *
     * @param userId 用户ID
     * @return 已扣费金额
     */
    BigDecimal getChargedAmount(Long userId);

    /**
     * 计算用户待扣费金额
     *
     * @param userId 用户ID
     * @return 待扣费金额
     */
    BigDecimal getPendingAmount(Long userId);

    /**
     * 计算服务器实例总计费金额
     *
     * @param serverInstanceId 服务器实例ID
     * @return 总计费金额
     */
    BigDecimal getServerInstanceTotalBillingAmount(Long serverInstanceId);

    /**
     * 统计用户计费记录数量
     *
     * @param userId 用户ID
     * @return 计费记录数量
     */
    long countUserBillingRecords(Long userId);

    /**
     * 统计用户已扣费记录数量
     *
     * @param userId 用户ID
     * @return 已扣费记录数量
     */
    long countUserChargedRecords(Long userId);

    /**
     * 统计用户待扣费记录数量
     *
     * @param userId 用户ID
     * @return 待扣费记录数量
     */
    long countUserPendingRecords(Long userId);
}
