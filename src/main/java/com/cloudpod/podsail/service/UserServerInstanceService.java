package com.cloudpod.podsail.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.db.entity.UserServerInstance;
import com.cloudpod.podsail.dto.instance.UserServerInstanceCreateDTO;
import com.cloudpod.podsail.dto.instance.UserServerInstanceQueryDTO;
import com.cloudpod.podsail.dto.instance.UserServerInstanceResponseDTO;
import com.cloudpod.podsail.dto.instance.UserServerInstanceUpdateDTO;
import com.cloudpod.podsail.service.base.BaseService;

import java.util.List;

/**
 * 用户服务器实例服务接口
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
public interface UserServerInstanceService extends BaseService<UserServerInstance> {

    /**
     * 创建用户服务器实例
     *
     * @param createDTO 创建用户服务器实例DTO
     * @return 用户服务器实例响应DTO
     */
    UserServerInstanceResponseDTO createUserServerInstance(UserServerInstanceCreateDTO createDTO);

    /**
     * 更新用户服务器实例
     *
     * @param updateDTO 更新用户服务器实例DTO
     * @return 用户服务器实例响应DTO
     */
    UserServerInstanceResponseDTO updateUserServerInstance(UserServerInstanceUpdateDTO updateDTO);

    /**
     * 根据ID获取用户服务器实例
     *
     * @param id 用户服务器实例ID
     * @return 用户服务器实例响应DTO
     */
    UserServerInstanceResponseDTO getUserServerInstanceById(Long id);

    /**
     * 分页查询用户服务器实例
     *
     * @param queryDTO 查询条件DTO
     * @return 分页结果
     */
    IPage<UserServerInstanceResponseDTO> getUserServerInstancePage(UserServerInstanceQueryDTO queryDTO);

    /**
     * 根据条件查询用户服务器实例列表
     *
     * @param queryDTO 查询条件DTO
     * @return 用户服务器实例响应DTO列表
     */
    List<UserServerInstanceResponseDTO> getUserServerInstanceList(UserServerInstanceQueryDTO queryDTO);

    /**
     * 根据服务器实例ID查询用户服务器实例
     *
     * @param serverId 服务器实例ID
     * @return 用户服务器实例实体
     */
    UserServerInstance getUserServerInstanceByServerId(String serverId);

    /**
     * 根据用户ID查询用户服务器实例列表
     *
     * @param userId 用户ID
     * @return 用户服务器实例实体列表
     */
    List<UserServerInstance> getUserServerInstancesByUserId(Long userId);

    /**
     * 根据用户ID和实例状态查询用户服务器实例列表
     *
     * @param userId         用户ID
     * @param instanceStatus 实例状态
     * @return 用户服务器实例实体列表
     */
    List<UserServerInstance> getUserServerInstancesByUserIdAndStatus(Long userId, Integer instanceStatus);

    /**
     * 根据订单ID查询用户服务器实例列表
     *
     * @param orderId 订单ID
     * @return 用户服务器实例实体列表
     */
    List<UserServerInstance> getUserServerInstancesByOrderId(Long orderId);

    /**
     * 根据区域ID查询用户服务器实例列表
     *
     * @param regionId 区域ID
     * @return 用户服务器实例实体列表
     */
    List<UserServerInstance> getUserServerInstancesByRegionId(String regionId);

    /**
     * 根据区域ID和可用区ID查询用户服务器实例列表
     *
     * @param regionId 区域ID
     * @param zoneId   可用区ID
     * @return 用户服务器实例实体列表
     */
    List<UserServerInstance> getUserServerInstancesByRegionIdAndZoneId(String regionId, String zoneId);

    /**
     * 检查服务器实例ID是否存在
     *
     * @param serverId 服务器实例ID
     * @return 是否存在
     */
    boolean existsByServerId(String serverId);

    /**
     * 启动服务器实例
     *
     * @param id 用户服务器实例ID
     * @return 是否启动成功
     */
    boolean startServerInstance(Long id);

    /**
     * 停止服务器实例
     *
     * @param id 用户服务器实例ID
     * @return 是否停止成功
     */
    boolean stopServerInstance(Long id);

    /**
     * 销毁服务器实例
     *
     * @param id 用户服务器实例ID
     * @return 是否销毁成功
     */
    boolean destroyServerInstance(Long id);

    /**
     * 续费服务器实例
     *
     * @param id         用户服务器实例ID
     * @param expireTime 新的到期时间
     * @return 是否续费成功
     */
    boolean renewServerInstance(Long id, Long expireTime);

    /**
     * 设置自动续费
     *
     * @param id        用户服务器实例ID
     * @param autoRenew 是否自动续费
     * @return 是否设置成功
     */
    boolean setAutoRenew(Long id, Integer autoRenew);

    /**
     * 查询即将到期的服务器实例
     *
     * @param days 提前天数
     * @return 即将到期的服务器实例列表
     */
    List<UserServerInstance> getExpiringServerInstances(int days);

    /**
     * 查询已过期的服务器实例
     *
     * @return 已过期的服务器实例列表
     */
    List<UserServerInstance> getExpiredServerInstances();

    /**
     * 查询需要自动续费的服务器实例
     *
     * @return 需要自动续费的服务器实例列表
     */
    List<UserServerInstance> getAutoRenewServerInstances();

    /**
     * 统计用户服务器实例数量
     *
     * @param userId 用户ID
     * @return 服务器实例数量
     */
    long countUserServerInstances(Long userId);

    /**
     * 统计用户运行中的服务器实例数量
     *
     * @param userId 用户ID
     * @return 运行中的服务器实例数量
     */
    long countUserRunningServerInstances(Long userId);

    /**
     * 统计用户已停止的服务器实例数量
     *
     * @param userId 用户ID
     * @return 已停止的服务器实例数量
     */
    long countUserStoppedServerInstances(Long userId);
}
