package com.cloudpod.podsail.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.db.entity.UserOrder;
import com.cloudpod.podsail.dto.order.UserOrderCreateDTO;
import com.cloudpod.podsail.dto.order.UserOrderQueryDTO;
import com.cloudpod.podsail.dto.order.UserOrderResponseDTO;
import com.cloudpod.podsail.dto.order.UserOrderUpdateDTO;
import com.cloudpod.podsail.service.base.BaseService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户订单服务接口
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
public interface UserOrderService extends BaseService<UserOrder> {

    /**
     * 创建用户订单
     *
     * @param createDTO 创建用户订单DTO
     * @return 用户订单响应DTO
     */
    UserOrderResponseDTO createUserOrder(UserOrderCreateDTO createDTO);

    /**
     * 更新用户订单
     *
     * @param updateDTO 更新用户订单DTO
     * @return 用户订单响应DTO
     */
    UserOrderResponseDTO updateUserOrder(UserOrderUpdateDTO updateDTO);

    /**
     * 根据ID获取用户订单
     *
     * @param id 用户订单ID
     * @return 用户订单响应DTO
     */
    UserOrderResponseDTO getUserOrderById(Long id);

    /**
     * 分页查询用户订单
     *
     * @param queryDTO 查询条件DTO
     * @return 分页结果
     */
    IPage<UserOrderResponseDTO> getUserOrderPage(UserOrderQueryDTO queryDTO);

    /**
     * 根据条件查询用户订单列表
     *
     * @param queryDTO 查询条件DTO
     * @return 用户订单响应DTO列表
     */
    List<UserOrderResponseDTO> getUserOrderList(UserOrderQueryDTO queryDTO);

    /**
     * 根据订单号查询用户订单
     *
     * @param orderNo 订单号
     * @return 用户订单实体
     */
    UserOrder getUserOrderByOrderNo(String orderNo);

    /**
     * 根据用户ID查询用户订单列表
     *
     * @param userId 用户ID
     * @return 用户订单实体列表
     */
    List<UserOrder> getUserOrdersByUserId(Long userId);

    /**
     * 根据用户ID和订单状态查询用户订单列表
     *
     * @param userId      用户ID
     * @param orderStatus 订单状态
     * @return 用户订单实体列表
     */
    List<UserOrder> getUserOrdersByUserIdAndStatus(Long userId, Integer orderStatus);

    /**
     * 检查订单号是否存在
     *
     * @param orderNo 订单号
     * @return 是否存在
     */
    boolean existsByOrderNo(String orderNo);

    /**
     * 支付订单
     *
     * @param orderId 订单ID
     * @return 是否支付成功
     */
    boolean payOrder(Long orderId);

    /**
     * 取消订单
     *
     * @param orderId 订单ID
     * @return 是否取消成功
     */
    boolean cancelOrder(Long orderId);

    /**
     * 退款订单
     *
     * @param orderId 订单ID
     * @return 是否退款成功
     */
    boolean refundOrder(Long orderId);

    /**
     * 查询过期未支付的订单
     *
     * @return 过期未支付的订单列表
     */
    List<UserOrder> getExpiredUnpaidOrders();

    /**
     * 自动取消过期未支付的订单
     *
     * @return 取消的订单数量
     */
    int autoCancelExpiredOrders();

    /**
     * 计算用户总订单金额
     *
     * @param userId 用户ID
     * @return 总订单金额
     */
    BigDecimal getTotalOrderAmount(Long userId);

    /**
     * 计算用户已支付订单金额
     *
     * @param userId 用户ID
     * @return 已支付订单金额
     */
    BigDecimal getPaidOrderAmount(Long userId);

    /**
     * 统计用户订单数量
     *
     * @param userId 用户ID
     * @return 订单数量
     */
    long countUserOrders(Long userId);

    /**
     * 统计用户已支付订单数量
     *
     * @param userId 用户ID
     * @return 已支付订单数量
     */
    long countUserPaidOrders(Long userId);
}
