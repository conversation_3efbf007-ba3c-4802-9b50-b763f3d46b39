package com.cloudpod.podsail.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.db.entity.UserBalanceLog;
import com.cloudpod.podsail.dto.user.UserBalanceLogCreateDTO;
import com.cloudpod.podsail.dto.user.UserBalanceLogQueryDTO;
import com.cloudpod.podsail.dto.user.UserBalanceLogResponseDTO;
import com.cloudpod.podsail.dto.user.UserBalanceLogUpdateDTO;
import com.cloudpod.podsail.service.base.BaseService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户余额流水服务接口
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
public interface UserBalanceLogService extends BaseService<UserBalanceLog> {

    /**
     * 创建用户余额流水
     *
     * @param createDTO 创建用户余额流水DTO
     * @return 用户余额流水响应DTO
     */
    UserBalanceLogResponseDTO createUserBalanceLog(UserBalanceLogCreateDTO createDTO);

    /**
     * 更新用户余额流水
     *
     * @param updateDTO 更新用户余额流水DTO
     * @return 用户余额流水响应DTO
     */
    UserBalanceLogResponseDTO updateUserBalanceLog(UserBalanceLogUpdateDTO updateDTO);

    /**
     * 根据ID获取用户余额流水
     *
     * @param id 用户余额流水ID
     * @return 用户余额流水响应DTO
     */
    UserBalanceLogResponseDTO getUserBalanceLogById(Long id);

    /**
     * 分页查询用户余额流水
     *
     * @param queryDTO 查询条件DTO
     * @return 分页结果
     */
    IPage<UserBalanceLogResponseDTO> getUserBalanceLogPage(UserBalanceLogQueryDTO queryDTO);

    /**
     * 根据条件查询用户余额流水列表
     *
     * @param queryDTO 查询条件DTO
     * @return 用户余额流水响应DTO列表
     */
    List<UserBalanceLogResponseDTO> getUserBalanceLogList(UserBalanceLogQueryDTO queryDTO);

    /**
     * 根据用户ID查询余额流水列表
     *
     * @param userId 用户ID
     * @return 用户余额流水实体列表
     */
    List<UserBalanceLog> getUserBalanceLogsByUserId(Long userId);

    /**
     * 根据用户ID和类型查询余额流水列表
     *
     * @param userId 用户ID
     * @param type   余额类型
     * @return 用户余额流水实体列表
     */
    List<UserBalanceLog> getUserBalanceLogsByUserIdAndType(Long userId, Integer type);

    /**
     * 根据交易ID查询余额流水
     *
     * @param tradeId 交易ID
     * @return 用户余额流水实体
     */
    UserBalanceLog getUserBalanceLogByTradeId(Long tradeId);

    /**
     * 计算用户总充值金额
     *
     * @param userId 用户ID
     * @return 总充值金额
     */
    BigDecimal getTotalRechargeAmount(Long userId);

    /**
     * 计算用户总消费金额
     *
     * @param userId 用户ID
     * @return 总消费金额
     */
    BigDecimal getTotalConsumeAmount(Long userId);

    /**
     * 计算用户总退款金额
     *
     * @param userId 用户ID
     * @return 总退款金额
     */
    BigDecimal getTotalRefundAmount(Long userId);

    /**
     * 创建充值流水
     *
     * @param userId  用户ID
     * @param amount  充值金额
     * @param tradeId 交易ID
     * @param remark  备注
     * @return 用户余额流水响应DTO
     */
    UserBalanceLogResponseDTO createRechargeLog(Long userId, BigDecimal amount, Long tradeId, String remark);

    /**
     * 创建消费流水
     *
     * @param userId  用户ID
     * @param amount  消费金额
     * @param tradeId 交易ID
     * @param remark  备注
     * @return 用户余额流水响应DTO
     */
    UserBalanceLogResponseDTO createConsumeLog(Long userId, BigDecimal amount, Long tradeId, String remark);

    /**
     * 创建退款流水
     *
     * @param userId  用户ID
     * @param amount  退款金额
     * @param tradeId 交易ID
     * @param remark  备注
     * @return 用户余额流水响应DTO
     */
    UserBalanceLogResponseDTO createRefundLog(Long userId, BigDecimal amount, Long tradeId, String remark);
}
