package com.cloudpod.podsail.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.db.entity.UserApiKey;
import com.cloudpod.podsail.dto.user.UserApiKeyCreateDTO;
import com.cloudpod.podsail.dto.user.UserApiKeyQueryDTO;
import com.cloudpod.podsail.dto.user.UserApiKeyResponseDTO;
import com.cloudpod.podsail.dto.user.UserApiKeyUpdateDTO;
import com.cloudpod.podsail.service.base.BaseService;

import java.util.List;

/**
 * 用户API密钥服务接口
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
public interface UserApiKeyService extends BaseService<UserApiKey> {

    /**
     * 创建用户API密钥
     *
     * @param createDTO 创建用户API密钥DTO
     * @return 用户API密钥响应DTO
     */
    UserApiKeyResponseDTO createUserApiKey(UserApiKeyCreateDTO createDTO);

    /**
     * 更新用户API密钥
     *
     * @param updateDTO 更新用户API密钥DTO
     * @return 用户API密钥响应DTO
     */
    UserApiKeyResponseDTO updateUserApiKey(UserApiKeyUpdateDTO updateDTO);

    /**
     * 根据ID获取用户API密钥
     *
     * @param id 用户API密钥ID
     * @return 用户API密钥响应DTO
     */
    UserApiKeyResponseDTO getUserApiKeyById(Long id);

    /**
     * 分页查询用户API密钥
     *
     * @param queryDTO 查询条件DTO
     * @return 分页结果
     */
    IPage<UserApiKeyResponseDTO> getUserApiKeyPage(UserApiKeyQueryDTO queryDTO);

    /**
     * 根据条件查询用户API密钥列表
     *
     * @param queryDTO 查询条件DTO
     * @return 用户API密钥响应DTO列表
     */
    List<UserApiKeyResponseDTO> getUserApiKeyList(UserApiKeyQueryDTO queryDTO);

    /**
     * 根据API密钥查询用户API密钥
     *
     * @param apiKey API密钥
     * @return 用户API密钥实体
     */
    UserApiKey getUserApiKeyByApiKey(String apiKey);

    /**
     * 根据用户ID查询用户API密钥列表
     *
     * @param userId 用户ID
     * @return 用户API密钥实体列表
     */
    List<UserApiKey> getUserApiKeysByUserId(Long userId);

    /**
     * 检查API密钥是否存在
     *
     * @param apiKey API密钥
     * @return 是否存在
     */
    boolean existsByApiKey(String apiKey);

    /**
     * 吊销用户API密钥
     *
     * @param id 用户API密钥ID
     * @return 是否吊销成功
     */
    boolean revokeUserApiKey(Long id);

    /**
     * 激活用户API密钥
     *
     * @param id 用户API密钥ID
     * @return 是否激活成功
     */
    boolean activateUserApiKey(Long id);

    /**
     * 更新API密钥最近访问时间
     *
     * @param apiKey API密钥
     * @return 是否更新成功
     */
    boolean updateLastVisitTime(String apiKey);

    /**
     * 验证API密钥是否有效
     *
     * @param apiKey API密钥
     * @return 是否有效
     */
    boolean validateApiKey(String apiKey);

    /**
     * 重新生成API密钥
     *
     * @param id 用户API密钥ID
     * @return 新的API密钥
     */
    String regenerateApiKey(Long id);
}
