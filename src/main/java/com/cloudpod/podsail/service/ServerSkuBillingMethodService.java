package com.cloudpod.podsail.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.db.entity.ServerSkuBillingMethod;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodCreateDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodQueryDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodResponseDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodUpdateDTO;
import com.cloudpod.podsail.service.base.BaseService;

import java.util.List;

/**
 * 主机套餐规格定价服务接口
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
public interface ServerSkuBillingMethodService extends BaseService<ServerSkuBillingMethod> {

    /**
     * 创建主机套餐规格定价
     *
     * @param createDTO 创建主机套餐规格定价DTO
     * @return 主机套餐规格定价响应DTO
     */
    ServerSkuBillingMethodResponseDTO createServerSkuBillingMethod(ServerSkuBillingMethodCreateDTO createDTO);

    /**
     * 更新主机套餐规格定价
     *
     * @param updateDTO 更新主机套餐规格定价DTO
     * @return 主机套餐规格定价响应DTO
     */
    ServerSkuBillingMethodResponseDTO updateServerSkuBillingMethod(ServerSkuBillingMethodUpdateDTO updateDTO);

    /**
     * 根据ID获取主机套餐规格定价
     *
     * @param id 主机套餐规格定价ID
     * @return 主机套餐规格定价响应DTO
     */
    ServerSkuBillingMethodResponseDTO getServerSkuBillingMethodById(Long id);

    /**
     * 分页查询主机套餐规格定价
     *
     * @param queryDTO 查询条件DTO
     * @return 分页结果
     */
    IPage<ServerSkuBillingMethodResponseDTO> getServerSkuBillingMethodPage(ServerSkuBillingMethodQueryDTO queryDTO);

    /**
     * 根据条件查询主机套餐规格定价列表
     *
     * @param queryDTO 查询条件DTO
     * @return 主机套餐规格定价响应DTO列表
     */
    List<ServerSkuBillingMethodResponseDTO> getServerSkuBillingMethodList(ServerSkuBillingMethodQueryDTO queryDTO);

    /**
     * 根据服务器套餐SKU ID查询定价列表
     *
     * @param serverSkuId 服务器套餐SKU ID
     * @return 主机套餐规格定价实体列表
     */
    List<ServerSkuBillingMethod> getServerSkuBillingMethodsByServerSkuId(String serverSkuId);

    /**
     * 根据服务器套餐SKU ID和计费类型查询定价列表
     *
     * @param serverSkuId 服务器套餐SKU ID
     * @param billingType 计费类型
     * @return 主机套餐规格定价实体列表
     */
    List<ServerSkuBillingMethod> getServerSkuBillingMethodsByServerSkuIdAndBillingType(String serverSkuId, Integer billingType);

    /**
     * 根据服务器套餐SKU ID、计费类型和计费周期查询定价
     *
     * @param serverSkuId  服务器套餐SKU ID
     * @param billingType  计费类型
     * @param billingCycle 计费周期
     * @return 主机套餐规格定价实体
     */
    ServerSkuBillingMethod getServerSkuBillingMethodBySkuAndTypeAndCycle(String serverSkuId, Integer billingType, String billingCycle);

    /**
     * 根据区域ID查询定价列表
     *
     * @param regionId 区域ID
     * @return 主机套餐规格定价实体列表
     */
    List<ServerSkuBillingMethod> getServerSkuBillingMethodsByRegionId(String regionId);

    /**
     * 根据区域ID和可用区ID查询定价列表
     *
     * @param regionId 区域ID
     * @param zoneId   可用区ID
     * @return 主机套餐规格定价实体列表
     */
    List<ServerSkuBillingMethod> getServerSkuBillingMethodsByRegionIdAndZoneId(String regionId, String zoneId);

    /**
     * 查询有效的定价列表
     *
     * @return 有效的主机套餐规格定价实体列表
     */
    List<ServerSkuBillingMethod> getEffectiveServerSkuBillingMethods();

    /**
     * 查询过期的定价列表
     *
     * @return 过期的主机套餐规格定价实体列表
     */
    List<ServerSkuBillingMethod> getExpiredServerSkuBillingMethods();

    /**
     * 启用定价
     *
     * @param id 主机套餐规格定价ID
     * @return 是否启用成功
     */
    boolean enableServerSkuBillingMethod(Long id);

    /**
     * 禁用定价
     *
     * @param id 主机套餐规格定价ID
     * @return 是否禁用成功
     */
    boolean disableServerSkuBillingMethod(Long id);

    /**
     * 自动禁用过期的定价
     *
     * @return 禁用的定价数量
     */
    int autoDisableExpiredBillingMethods();

    /**
     * 检查定价是否有效
     *
     * @param id 主机套餐规格定价ID
     * @return 是否有效
     */
    boolean isServerSkuBillingMethodEffective(Long id);

    /**
     * 检查定价组合是否存在
     *
     * @param serverSkuId  服务器套餐SKU ID
     * @param billingType  计费类型
     * @param billingCycle 计费周期
     * @param regionId     区域ID
     * @param zoneId       可用区ID
     * @return 是否存在
     */
    boolean existsBySkuAndTypeAndCycleAndRegionAndZone(String serverSkuId, Integer billingType, String billingCycle, String regionId, String zoneId);
}
