package com.cloudpod.podsail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.common.util.BeanCopyUtil;
import com.cloudpod.podsail.db.dao.ServerSkuBillingMethodDao;
import com.cloudpod.podsail.db.entity.ServerSkuBillingMethod;
import com.cloudpod.podsail.db.mapper.ServerSkuBillingMethodMapper;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodCreateDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodQueryDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodResponseDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodUpdateDTO;
import com.cloudpod.podsail.service.ServerSkuBillingMethodService;
import com.cloudpod.podsail.service.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 主机套餐规格定价服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Service
public class ServerSkuBillingMethodServiceImpl extends BaseServiceImpl<ServerSkuBillingMethodMapper, ServerSkuBillingMethod> implements ServerSkuBillingMethodService {

    @Autowired
    private ServerSkuBillingMethodDao serverSkuBillingMethodDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServerSkuBillingMethodResponseDTO createServerSkuBillingMethod(ServerSkuBillingMethodCreateDTO createDTO) {
        // 检查定价组合是否已存在
        if (existsBySkuAndTypeAndCycleAndRegionAndZone(
                createDTO.getServerSkuId(), 
                createDTO.getBillingType(), 
                createDTO.getBillingCycle(),
                createDTO.getRegionId(),
                createDTO.getZoneId())) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "该定价组合已存在");
        }

        ServerSkuBillingMethod serverSkuBillingMethod = BeanCopyUtil.copyProperties(createDTO, ServerSkuBillingMethod.class);
        
        // 设置默认值
        if (serverSkuBillingMethod.getDiscountRate() == null) {
            serverSkuBillingMethod.setDiscountRate(BigDecimal.ONE);
        }
        if (!StringUtils.hasText(serverSkuBillingMethod.getCurrency())) {
            serverSkuBillingMethod.setCurrency("CNY");
        }
        if (!StringUtils.hasText(serverSkuBillingMethod.getRegionId())) {
            serverSkuBillingMethod.setRegionId("");
        }
        if (!StringUtils.hasText(serverSkuBillingMethod.getZoneId())) {
            serverSkuBillingMethod.setZoneId("");
        }
        if (serverSkuBillingMethod.getExpireTime() == null) {
            serverSkuBillingMethod.setExpireTime(0L);
        }
        if (serverSkuBillingMethod.getStatus() == null) {
            serverSkuBillingMethod.setStatus(1); // 1-正常
        }

        ServerSkuBillingMethod createdServerSkuBillingMethod = createEntity(serverSkuBillingMethod);
        return BeanCopyUtil.copyProperties(createdServerSkuBillingMethod, ServerSkuBillingMethodResponseDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServerSkuBillingMethodResponseDTO updateServerSkuBillingMethod(ServerSkuBillingMethodUpdateDTO updateDTO) {
        ServerSkuBillingMethod existingServerSkuBillingMethod = getEntityById(updateDTO.getId());

        // 复制属性（忽略null值和不可修改的字段）
        BeanCopyUtil.copyPropertiesToExisting(updateDTO, existingServerSkuBillingMethod, 
            "serverSkuId", "billingType", "billingCycle");

        ServerSkuBillingMethod updatedServerSkuBillingMethod = updateEntity(existingServerSkuBillingMethod);
        return BeanCopyUtil.copyProperties(updatedServerSkuBillingMethod, ServerSkuBillingMethodResponseDTO.class);
    }

    @Override
    public ServerSkuBillingMethodResponseDTO getServerSkuBillingMethodById(Long id) {
        ServerSkuBillingMethod serverSkuBillingMethod = getEntityById(id);
        return BeanCopyUtil.copyProperties(serverSkuBillingMethod, ServerSkuBillingMethodResponseDTO.class);
    }

    @Override
    public IPage<ServerSkuBillingMethodResponseDTO> getServerSkuBillingMethodPage(ServerSkuBillingMethodQueryDTO queryDTO) {
        IPage<ServerSkuBillingMethod> page = queryDTO.page();
        QueryWrapper<ServerSkuBillingMethod> queryWrapper = buildServerSkuBillingMethodQueryWrapper(queryDTO);
        
        IPage<ServerSkuBillingMethod> serverSkuBillingMethodPage = page(page, queryWrapper);
        
        // 转换为响应DTO
        List<ServerSkuBillingMethodResponseDTO> responseDTOList = BeanCopyUtil.copyPropertiesList(
            serverSkuBillingMethodPage.getRecords(), ServerSkuBillingMethodResponseDTO.class);
        
        IPage<ServerSkuBillingMethodResponseDTO> responsePage = new Page<>(
            serverSkuBillingMethodPage.getCurrent(), serverSkuBillingMethodPage.getSize(), serverSkuBillingMethodPage.getTotal());
        responsePage.setRecords(responseDTOList);
        
        return responsePage;
    }

    @Override
    public List<ServerSkuBillingMethodResponseDTO> getServerSkuBillingMethodList(ServerSkuBillingMethodQueryDTO queryDTO) {
        QueryWrapper<ServerSkuBillingMethod> queryWrapper = buildServerSkuBillingMethodQueryWrapper(queryDTO);
        List<ServerSkuBillingMethod> serverSkuBillingMethodList = list(queryWrapper);
        return BeanCopyUtil.copyPropertiesList(serverSkuBillingMethodList, ServerSkuBillingMethodResponseDTO.class);
    }

    @Override
    public List<ServerSkuBillingMethod> getServerSkuBillingMethodsByServerSkuId(String serverSkuId) {
        if (!StringUtils.hasText(serverSkuId)) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "服务器套餐SKU ID不能为空");
        }
        QueryWrapper<ServerSkuBillingMethod> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("server_sku_id", serverSkuId);
        queryWrapper.orderByAsc("billing_type", "billing_cycle");
        return list(queryWrapper);
    }

    @Override
    public List<ServerSkuBillingMethod> getServerSkuBillingMethodsByServerSkuIdAndBillingType(String serverSkuId, Integer billingType) {
        if (!StringUtils.hasText(serverSkuId) || billingType == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "服务器套餐SKU ID和计费类型不能为空");
        }
        QueryWrapper<ServerSkuBillingMethod> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("server_sku_id", serverSkuId);
        queryWrapper.eq("billing_type", billingType);
        queryWrapper.orderByAsc("billing_cycle");
        return list(queryWrapper);
    }

    @Override
    public ServerSkuBillingMethod getServerSkuBillingMethodBySkuAndTypeAndCycle(String serverSkuId, Integer billingType, String billingCycle) {
        if (!StringUtils.hasText(serverSkuId) || billingType == null || !StringUtils.hasText(billingCycle)) {
            return null;
        }
        QueryWrapper<ServerSkuBillingMethod> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("server_sku_id", serverSkuId);
        queryWrapper.eq("billing_type", billingType);
        queryWrapper.eq("billing_cycle", billingCycle);
        return getOne(queryWrapper);
    }

    @Override
    public List<ServerSkuBillingMethod> getServerSkuBillingMethodsByRegionId(String regionId) {
        if (!StringUtils.hasText(regionId)) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "区域ID不能为空");
        }
        QueryWrapper<ServerSkuBillingMethod> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("region_id", regionId);
        queryWrapper.orderByAsc("server_sku_id", "billing_type", "billing_cycle");
        return list(queryWrapper);
    }

    @Override
    public List<ServerSkuBillingMethod> getServerSkuBillingMethodsByRegionIdAndZoneId(String regionId, String zoneId) {
        if (!StringUtils.hasText(regionId) || !StringUtils.hasText(zoneId)) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "区域ID和可用区ID不能为空");
        }
        QueryWrapper<ServerSkuBillingMethod> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("region_id", regionId);
        queryWrapper.eq("zone_id", zoneId);
        queryWrapper.orderByAsc("server_sku_id", "billing_type", "billing_cycle");
        return list(queryWrapper);
    }

    @Override
    public List<ServerSkuBillingMethod> getEffectiveServerSkuBillingMethods() {
        QueryWrapper<ServerSkuBillingMethod> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1); // 1-正常
        queryWrapper.le("effective_time", System.currentTimeMillis());
        queryWrapper.and(wrapper -> wrapper.eq("expire_time", 0).or().gt("expire_time", System.currentTimeMillis()));
        queryWrapper.orderByAsc("server_sku_id", "billing_type", "billing_cycle");
        return list(queryWrapper);
    }

    @Override
    public List<ServerSkuBillingMethod> getExpiredServerSkuBillingMethods() {
        QueryWrapper<ServerSkuBillingMethod> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1); // 1-正常
        queryWrapper.gt("expire_time", 0); // 有过期时间
        queryWrapper.lt("expire_time", System.currentTimeMillis()); // 已过期
        return list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableServerSkuBillingMethod(Long id) {
        ServerSkuBillingMethod serverSkuBillingMethod = getEntityById(id);
        serverSkuBillingMethod.setStatus(1); // 1-正常
        return updateById(serverSkuBillingMethod);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableServerSkuBillingMethod(Long id) {
        ServerSkuBillingMethod serverSkuBillingMethod = getEntityById(id);
        serverSkuBillingMethod.setStatus(2); // 2-已失效
        return updateById(serverSkuBillingMethod);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int autoDisableExpiredBillingMethods() {
        List<ServerSkuBillingMethod> expiredBillingMethods = getExpiredServerSkuBillingMethods();
        if (expiredBillingMethods.isEmpty()) {
            return 0;
        }
        
        int disabledCount = 0;
        for (ServerSkuBillingMethod billingMethod : expiredBillingMethods) {
            try {
                disableServerSkuBillingMethod(billingMethod.getId());
                disabledCount++;
            } catch (Exception e) {
                log.error("自动禁用过期定价失败，定价ID: {}", billingMethod.getId(), e);
            }
        }
        
        log.info("自动禁用过期定价完成，总数: {}, 成功: {}", expiredBillingMethods.size(), disabledCount);
        return disabledCount;
    }

    @Override
    public boolean isServerSkuBillingMethodEffective(Long id) {
        ServerSkuBillingMethod serverSkuBillingMethod = getEntityById(id);
        
        // 检查状态
        if (serverSkuBillingMethod.getStatus() != 1) {
            return false;
        }
        
        long currentTime = System.currentTimeMillis();
        
        // 检查生效时间
        if (serverSkuBillingMethod.getEffectiveTime() > currentTime) {
            return false;
        }
        
        // 检查失效时间
        if (serverSkuBillingMethod.getExpireTime() != null && 
            serverSkuBillingMethod.getExpireTime() > 0 && 
            serverSkuBillingMethod.getExpireTime() <= currentTime) {
            return false;
        }
        
        return true;
    }

    @Override
    public boolean existsBySkuAndTypeAndCycleAndRegionAndZone(String serverSkuId, Integer billingType, String billingCycle, String regionId, String zoneId) {
        if (!StringUtils.hasText(serverSkuId) || billingType == null || !StringUtils.hasText(billingCycle)) {
            return false;
        }
        
        QueryWrapper<ServerSkuBillingMethod> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("server_sku_id", serverSkuId);
        queryWrapper.eq("billing_type", billingType);
        queryWrapper.eq("billing_cycle", billingCycle);
        
        if (StringUtils.hasText(regionId)) {
            queryWrapper.eq("region_id", regionId);
        } else {
            queryWrapper.eq("region_id", "");
        }
        
        if (StringUtils.hasText(zoneId)) {
            queryWrapper.eq("zone_id", zoneId);
        } else {
            queryWrapper.eq("zone_id", "");
        }
        
        return count(queryWrapper) > 0;
    }

    /**
     * 构建主机套餐规格定价查询条件
     */
    private QueryWrapper<ServerSkuBillingMethod> buildServerSkuBillingMethodQueryWrapper(ServerSkuBillingMethodQueryDTO queryDTO) {
        QueryWrapper<ServerSkuBillingMethod> queryWrapper = new QueryWrapper<>();

        if (queryDTO.getId() != null) {
            queryWrapper.eq("id", queryDTO.getId());
        }
        if (StringUtils.hasText(queryDTO.getServerSkuId())) {
            queryWrapper.eq("server_sku_id", queryDTO.getServerSkuId());
        }
        if (StringUtils.hasText(queryDTO.getServerSkuIdLike())) {
            queryWrapper.like("server_sku_id", queryDTO.getServerSkuIdLike());
        }
        if (queryDTO.getBillingType() != null) {
            queryWrapper.eq("billing_type", queryDTO.getBillingType());
        }
        if (StringUtils.hasText(queryDTO.getBillingCycle())) {
            queryWrapper.eq("billing_cycle", queryDTO.getBillingCycle());
        }
        if (queryDTO.getPriceMin() != null) {
            queryWrapper.ge("price", queryDTO.getPriceMin());
        }
        if (queryDTO.getPriceMax() != null) {
            queryWrapper.le("price", queryDTO.getPriceMax());
        }
        if (queryDTO.getOriginalPriceMin() != null) {
            queryWrapper.ge("original_price", queryDTO.getOriginalPriceMin());
        }
        if (queryDTO.getOriginalPriceMax() != null) {
            queryWrapper.le("original_price", queryDTO.getOriginalPriceMax());
        }
        if (queryDTO.getDiscountRateMin() != null) {
            queryWrapper.ge("discount_rate", queryDTO.getDiscountRateMin());
        }
        if (queryDTO.getDiscountRateMax() != null) {
            queryWrapper.le("discount_rate", queryDTO.getDiscountRateMax());
        }
        if (StringUtils.hasText(queryDTO.getCurrency())) {
            queryWrapper.eq("currency", queryDTO.getCurrency());
        }
        if (StringUtils.hasText(queryDTO.getRegionId())) {
            queryWrapper.eq("region_id", queryDTO.getRegionId());
        }
        if (StringUtils.hasText(queryDTO.getRegionIdLike())) {
            queryWrapper.like("region_id", queryDTO.getRegionIdLike());
        }
        if (StringUtils.hasText(queryDTO.getZoneId())) {
            queryWrapper.eq("zone_id", queryDTO.getZoneId());
        }
        if (StringUtils.hasText(queryDTO.getZoneIdLike())) {
            queryWrapper.like("zone_id", queryDTO.getZoneIdLike());
        }
        if (queryDTO.getEffectiveTimeStart() != null) {
            queryWrapper.ge("effective_time", queryDTO.getEffectiveTimeStart());
        }
        if (queryDTO.getEffectiveTimeEnd() != null) {
            queryWrapper.le("effective_time", queryDTO.getEffectiveTimeEnd());
        }
        if (queryDTO.getExpireTimeStart() != null) {
            queryWrapper.ge("expire_time", queryDTO.getExpireTimeStart());
        }
        if (queryDTO.getExpireTimeEnd() != null) {
            queryWrapper.le("expire_time", queryDTO.getExpireTimeEnd());
        }
        if (queryDTO.getStatus() != null) {
            queryWrapper.eq("status", queryDTO.getStatus());
        }
        if (StringUtils.hasText(queryDTO.getRemark())) {
            queryWrapper.eq("remark", queryDTO.getRemark());
        }
        if (StringUtils.hasText(queryDTO.getRemarkLike())) {
            queryWrapper.like("remark", queryDTO.getRemarkLike());
        }
        if (queryDTO.getCreatedAtStart() != null) {
            queryWrapper.ge("created_at", queryDTO.getCreatedAtStart());
        }
        if (queryDTO.getCreatedAtEnd() != null) {
            queryWrapper.le("created_at", queryDTO.getCreatedAtEnd());
        }
        if (queryDTO.getCreatedUid() != null) {
            queryWrapper.eq("created_uid", queryDTO.getCreatedUid());
        }

        queryWrapper.orderByDesc("created_at");
        return queryWrapper;
    }
}
