package com.cloudpod.podsail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.common.util.BeanCopyUtil;
import com.cloudpod.podsail.db.dao.UserBalanceLogDao;
import com.cloudpod.podsail.db.entity.UserBalanceLog;
import com.cloudpod.podsail.db.mapper.UserBalanceLogMapper;
import com.cloudpod.podsail.dto.user.UserBalanceLogCreateDTO;
import com.cloudpod.podsail.dto.user.UserBalanceLogQueryDTO;
import com.cloudpod.podsail.dto.user.UserBalanceLogResponseDTO;
import com.cloudpod.podsail.dto.user.UserBalanceLogUpdateDTO;
import com.cloudpod.podsail.service.UserBalanceLogService;
import com.cloudpod.podsail.service.UserService;
import com.cloudpod.podsail.service.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户余额流水服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Service
public class UserBalanceLogServiceImpl extends BaseServiceImpl<UserBalanceLogMapper, UserBalanceLog> implements UserBalanceLogService {

    @Autowired
    private UserBalanceLogDao userBalanceLogDao;

    @Autowired
    private UserService userService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserBalanceLogResponseDTO createUserBalanceLog(UserBalanceLogCreateDTO createDTO) {
        // 检查用户是否存在
        userService.getEntityById(createDTO.getUserId());

        UserBalanceLog userBalanceLog = BeanCopyUtil.copyProperties(createDTO, UserBalanceLog.class);

        UserBalanceLog createdUserBalanceLog = createEntity(userBalanceLog);
        return BeanCopyUtil.copyProperties(createdUserBalanceLog, UserBalanceLogResponseDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserBalanceLogResponseDTO updateUserBalanceLog(UserBalanceLogUpdateDTO updateDTO) {
        UserBalanceLog existingUserBalanceLog = getEntityById(updateDTO.getId());

        // 复制属性（忽略null值和不可修改的字段）
        BeanCopyUtil.copyPropertiesToExisting(updateDTO, existingUserBalanceLog, "userId", "type", "amount");

        UserBalanceLog updatedUserBalanceLog = updateEntity(existingUserBalanceLog);
        return BeanCopyUtil.copyProperties(updatedUserBalanceLog, UserBalanceLogResponseDTO.class);
    }

    @Override
    public UserBalanceLogResponseDTO getUserBalanceLogById(Long id) {
        UserBalanceLog userBalanceLog = getEntityById(id);
        return BeanCopyUtil.copyProperties(userBalanceLog, UserBalanceLogResponseDTO.class);
    }

    @Override
    public IPage<UserBalanceLogResponseDTO> getUserBalanceLogPage(UserBalanceLogQueryDTO queryDTO) {
        IPage<UserBalanceLog> page = queryDTO.page();
        QueryWrapper<UserBalanceLog> queryWrapper = buildUserBalanceLogQueryWrapper(queryDTO);
        
        IPage<UserBalanceLog> userBalanceLogPage = page(page, queryWrapper);
        
        // 转换为响应DTO
        List<UserBalanceLogResponseDTO> responseDTOList = BeanCopyUtil.copyPropertiesList(
            userBalanceLogPage.getRecords(), UserBalanceLogResponseDTO.class);
        
        IPage<UserBalanceLogResponseDTO> responsePage = new Page<>(
            userBalanceLogPage.getCurrent(), userBalanceLogPage.getSize(), userBalanceLogPage.getTotal());
        responsePage.setRecords(responseDTOList);
        
        return responsePage;
    }

    @Override
    public List<UserBalanceLogResponseDTO> getUserBalanceLogList(UserBalanceLogQueryDTO queryDTO) {
        QueryWrapper<UserBalanceLog> queryWrapper = buildUserBalanceLogQueryWrapper(queryDTO);
        List<UserBalanceLog> userBalanceLogList = list(queryWrapper);
        return BeanCopyUtil.copyPropertiesList(userBalanceLogList, UserBalanceLogResponseDTO.class);
    }

    @Override
    public List<UserBalanceLog> getUserBalanceLogsByUserId(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }
        QueryWrapper<UserBalanceLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByDesc("created_at");
        return list(queryWrapper);
    }

    @Override
    public List<UserBalanceLog> getUserBalanceLogsByUserIdAndType(Long userId, Integer type) {
        if (userId == null || type == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID和类型不能为空");
        }
        QueryWrapper<UserBalanceLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("type", type);
        queryWrapper.orderByDesc("created_at");
        return list(queryWrapper);
    }

    @Override
    public UserBalanceLog getUserBalanceLogByTradeId(Long tradeId) {
        if (tradeId == null) {
            return null;
        }
        QueryWrapper<UserBalanceLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("trade_id", tradeId);
        return getOne(queryWrapper);
    }

    @Override
    public BigDecimal getTotalRechargeAmount(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }
        
        QueryWrapper<UserBalanceLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("type", 1); // 1-充值
        queryWrapper.select("IFNULL(SUM(amount), 0) as total");
        
        List<UserBalanceLog> logs = list(queryWrapper);
        if (logs.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        // 这里需要通过原生SQL查询来获取SUM结果，简化处理
        List<UserBalanceLog> rechargeLogs = getUserBalanceLogsByUserIdAndType(userId, 1);
        return rechargeLogs.stream()
                .map(UserBalanceLog::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal getTotalConsumeAmount(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }
        
        List<UserBalanceLog> consumeLogs = getUserBalanceLogsByUserIdAndType(userId, 2);
        return consumeLogs.stream()
                .map(UserBalanceLog::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal getTotalRefundAmount(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }
        
        List<UserBalanceLog> refundLogs = getUserBalanceLogsByUserIdAndType(userId, 3);
        return refundLogs.stream()
                .map(UserBalanceLog::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserBalanceLogResponseDTO createRechargeLog(Long userId, BigDecimal amount, Long tradeId, String remark) {
        UserBalanceLogCreateDTO createDTO = new UserBalanceLogCreateDTO()
                .setUserId(userId)
                .setType(1) // 1-充值
                .setAmount(amount)
                .setTradeId(tradeId)
                .setRemark(remark);
        
        return createUserBalanceLog(createDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserBalanceLogResponseDTO createConsumeLog(Long userId, BigDecimal amount, Long tradeId, String remark) {
        UserBalanceLogCreateDTO createDTO = new UserBalanceLogCreateDTO()
                .setUserId(userId)
                .setType(2) // 2-消费
                .setAmount(amount)
                .setTradeId(tradeId)
                .setRemark(remark);
        
        return createUserBalanceLog(createDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserBalanceLogResponseDTO createRefundLog(Long userId, BigDecimal amount, Long tradeId, String remark) {
        UserBalanceLogCreateDTO createDTO = new UserBalanceLogCreateDTO()
                .setUserId(userId)
                .setType(3) // 3-退款
                .setAmount(amount)
                .setTradeId(tradeId)
                .setRemark(remark);
        
        return createUserBalanceLog(createDTO);
    }

    /**
     * 构建用户余额流水查询条件
     */
    private QueryWrapper<UserBalanceLog> buildUserBalanceLogQueryWrapper(UserBalanceLogQueryDTO queryDTO) {
        QueryWrapper<UserBalanceLog> queryWrapper = new QueryWrapper<>();
        
        if (queryDTO.getId() != null) {
            queryWrapper.eq("id", queryDTO.getId());
        }
        if (queryDTO.getUserId() != null) {
            queryWrapper.eq("user_id", queryDTO.getUserId());
        }
        if (queryDTO.getType() != null) {
            queryWrapper.eq("type", queryDTO.getType());
        }
        if (queryDTO.getAmountMin() != null) {
            queryWrapper.ge("amount", queryDTO.getAmountMin());
        }
        if (queryDTO.getAmountMax() != null) {
            queryWrapper.le("amount", queryDTO.getAmountMax());
        }
        if (queryDTO.getTradeId() != null) {
            queryWrapper.eq("trade_id", queryDTO.getTradeId());
        }
        if (StringUtils.hasText(queryDTO.getRemark())) {
            queryWrapper.eq("remark", queryDTO.getRemark());
        }
        if (StringUtils.hasText(queryDTO.getRemarkLike())) {
            queryWrapper.like("remark", queryDTO.getRemarkLike());
        }
        if (queryDTO.getCreatedAtStart() != null) {
            queryWrapper.ge("created_at", queryDTO.getCreatedAtStart());
        }
        if (queryDTO.getCreatedAtEnd() != null) {
            queryWrapper.le("created_at", queryDTO.getCreatedAtEnd());
        }
        if (queryDTO.getCreatedUid() != null) {
            queryWrapper.eq("created_uid", queryDTO.getCreatedUid());
        }
        
        queryWrapper.orderByDesc("created_at");
        return queryWrapper;
    }
}
