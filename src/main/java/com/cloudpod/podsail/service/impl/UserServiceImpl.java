package com.cloudpod.podsail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.common.util.BeanCopyUtil;
import com.cloudpod.podsail.db.dao.UserDao;
import com.cloudpod.podsail.db.entity.User;
import com.cloudpod.podsail.db.mapper.UserMapper;
import com.cloudpod.podsail.dto.user.UserCreateDTO;
import com.cloudpod.podsail.dto.user.UserQueryDTO;
import com.cloudpod.podsail.dto.user.UserResponseDTO;
import com.cloudpod.podsail.dto.user.UserUpdateDTO;
import com.cloudpod.podsail.service.UserService;
import com.cloudpod.podsail.service.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

/**
 * 用户服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Service
public class UserServiceImpl extends BaseServiceImpl<UserMapper, User> implements UserService {

    @Autowired
    private UserDao userDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserResponseDTO createUser(UserCreateDTO createDTO) {
        // 检查用户名是否已存在
        if (existsByUsername(createDTO.getUsername())) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户名已存在");
        }

        // 检查用户唯一码是否已存在
        if (StringUtils.hasText(createDTO.getCode()) && existsByCode(createDTO.getCode())) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户唯一码已存在");
        }

        User user = BeanCopyUtil.copyProperties(createDTO, User.class);
        
        // 生成用户唯一码（如果未提供）
        if (!StringUtils.hasText(user.getCode())) {
            user.setCode(generateUserCode());
        }

        // 设置密码（这里简单处理，实际项目中应该加密）
        user.setPassword(createDTO.getPassword());

        // 设置默认余额
        if (user.getBalance() == null) {
            user.setBalance(BigDecimal.ZERO);
        }

        // 设置注册时间
        if (user.getRegisterTime() == null) {
            user.setRegisterTime(System.currentTimeMillis());
        }

        User createdUser = createEntity(user);
        return BeanCopyUtil.copyProperties(createdUser, UserResponseDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserResponseDTO updateUser(UserUpdateDTO updateDTO) {
        User existingUser = getEntityById(updateDTO.getId());

        // 检查用户名是否已被其他用户使用
        if (StringUtils.hasText(updateDTO.getUsername()) && 
            !updateDTO.getUsername().equals(existingUser.getUsername()) &&
            existsByUsername(updateDTO.getUsername())) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户名已被其他用户使用");
        }

        // 检查用户唯一码是否已被其他用户使用
        if (StringUtils.hasText(updateDTO.getCode()) && 
            !updateDTO.getCode().equals(existingUser.getCode()) &&
            existsByCode(updateDTO.getCode())) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户唯一码已被其他用户使用");
        }

        // 复制属性（忽略null值和密码）
        BeanCopyUtil.copyPropertiesToExisting(updateDTO, existingUser, "password");

        // 如果需要更新密码
        if (StringUtils.hasText(updateDTO.getPassword())) {
            existingUser.setPassword(updateDTO.getPassword());
        }

        User updatedUser = updateEntity(existingUser);
        return BeanCopyUtil.copyProperties(updatedUser, UserResponseDTO.class);
    }

    @Override
    public UserResponseDTO getUserById(Long id) {
        User user = getEntityById(id);
        return BeanCopyUtil.copyProperties(user, UserResponseDTO.class);
    }

    @Override
    public IPage<UserResponseDTO> getUserPage(UserQueryDTO queryDTO) {
        IPage<User> page = queryDTO.page();
        QueryWrapper<User> queryWrapper = buildUserQueryWrapper(queryDTO);
        
        IPage<User> userPage = page(page, queryWrapper);
        
        // 转换为响应DTO
        List<UserResponseDTO> responseDTOList = BeanCopyUtil.copyPropertiesList(
            userPage.getRecords(), UserResponseDTO.class);
        
        IPage<UserResponseDTO> responsePage = new Page<>(
            userPage.getCurrent(), userPage.getSize(), userPage.getTotal());
        responsePage.setRecords(responseDTOList);
        
        return responsePage;
    }

    @Override
    public List<UserResponseDTO> getUserList(UserQueryDTO queryDTO) {
        QueryWrapper<User> queryWrapper = buildUserQueryWrapper(queryDTO);
        List<User> userList = list(queryWrapper);
        return BeanCopyUtil.copyPropertiesList(userList, UserResponseDTO.class);
    }

    @Override
    public User getUserByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return null;
        }
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username);
        return getOne(queryWrapper);
    }

    @Override
    public User getUserByCode(String code) {
        if (!StringUtils.hasText(code)) {
            return null;
        }
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", code);
        return getOne(queryWrapper);
    }

    @Override
    public boolean existsByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return false;
        }
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username);
        return count(queryWrapper) > 0;
    }

    @Override
    public boolean existsByCode(String code) {
        if (!StringUtils.hasText(code)) {
            return false;
        }
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", code);
        return count(queryWrapper) > 0;
    }

    @Override
    public boolean validatePassword(String username, String password) {
        if (!StringUtils.hasText(username) || !StringUtils.hasText(password)) {
            return false;
        }
        
        User user = getUserByUsername(username);
        if (user == null) {
            return false;
        }
        
        // 简单密码验证（实际项目中应该使用加密验证）
        return password.equals(user.getPassword());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePassword(Long userId, String newPassword) {
        if (userId == null || !StringUtils.hasText(newPassword)) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID和新密码不能为空");
        }
        
        User user = getEntityById(userId);

        // 设置新密码（实际项目中应该加密）
        user.setPassword(newPassword);
        
        return updateById(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBalance(Long userId, BigDecimal amount) {
        if (userId == null || amount == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID和金额不能为空");
        }
        
        User user = getEntityById(userId);
        BigDecimal newBalance = user.getBalance().add(amount);
        
        if (newBalance.compareTo(BigDecimal.ZERO) < 0) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "余额不足");
        }
        
        user.setBalance(newBalance);
        return updateById(user);
    }

    /**
     * 构建用户查询条件
     */
    private QueryWrapper<User> buildUserQueryWrapper(UserQueryDTO queryDTO) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        
        if (queryDTO.getId() != null) {
            queryWrapper.eq("id", queryDTO.getId());
        }
        if (StringUtils.hasText(queryDTO.getUsername())) {
            queryWrapper.eq("username", queryDTO.getUsername());
        }
        if (StringUtils.hasText(queryDTO.getUsernameLike())) {
            queryWrapper.like("username", queryDTO.getUsernameLike());
        }
        if (StringUtils.hasText(queryDTO.getCode())) {
            queryWrapper.eq("code", queryDTO.getCode());
        }
        if (StringUtils.hasText(queryDTO.getCodeLike())) {
            queryWrapper.like("code", queryDTO.getCodeLike());
        }
        if (queryDTO.getBalanceMin() != null) {
            queryWrapper.ge("balance", queryDTO.getBalanceMin());
        }
        if (queryDTO.getBalanceMax() != null) {
            queryWrapper.le("balance", queryDTO.getBalanceMax());
        }
        if (queryDTO.getRegisterTimeStart() != null) {
            queryWrapper.ge("register_time", queryDTO.getRegisterTimeStart());
        }
        if (queryDTO.getRegisterTimeEnd() != null) {
            queryWrapper.le("register_time", queryDTO.getRegisterTimeEnd());
        }
        if (queryDTO.getCreatedAtStart() != null) {
            queryWrapper.ge("created_at", queryDTO.getCreatedAtStart());
        }
        if (queryDTO.getCreatedAtEnd() != null) {
            queryWrapper.le("created_at", queryDTO.getCreatedAtEnd());
        }
        if (queryDTO.getCreatedUid() != null) {
            queryWrapper.eq("created_uid", queryDTO.getCreatedUid());
        }
        
        queryWrapper.orderByDesc("created_at");
        return queryWrapper;
    }

    /**
     * 生成用户唯一码
     */
    private String generateUserCode() {
        String code;
        do {
            code = "USER" + UUID.randomUUID().toString().replace("-", "").substring(0, 8).toUpperCase();
        } while (existsByCode(code));
        return code;
    }
}
