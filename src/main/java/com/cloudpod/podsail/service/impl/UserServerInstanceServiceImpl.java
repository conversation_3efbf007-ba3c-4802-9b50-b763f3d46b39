package com.cloudpod.podsail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.common.util.BeanCopyUtil;
import com.cloudpod.podsail.db.dao.UserServerInstanceDao;
import com.cloudpod.podsail.db.entity.UserServerInstance;
import com.cloudpod.podsail.db.mapper.UserServerInstanceMapper;
import com.cloudpod.podsail.dto.instance.UserServerInstanceCreateDTO;
import com.cloudpod.podsail.dto.instance.UserServerInstanceQueryDTO;
import com.cloudpod.podsail.dto.instance.UserServerInstanceResponseDTO;
import com.cloudpod.podsail.dto.instance.UserServerInstanceUpdateDTO;
import com.cloudpod.podsail.service.UserOrderService;
import com.cloudpod.podsail.service.UserServerInstanceService;
import com.cloudpod.podsail.service.UserService;
import com.cloudpod.podsail.service.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 用户服务器实例服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Service
public class UserServerInstanceServiceImpl extends BaseServiceImpl<UserServerInstanceMapper, UserServerInstance> implements UserServerInstanceService {

    @Autowired
    private UserServerInstanceDao userServerInstanceDao;

    @Autowired
    private UserService userService;

    @Autowired
    private UserOrderService userOrderService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserServerInstanceResponseDTO createUserServerInstance(UserServerInstanceCreateDTO createDTO) {
        // 检查用户是否存在
        userService.getEntityById(createDTO.getUserId());

        // 检查订单是否存在
        userOrderService.getEntityById(createDTO.getOrderId());

        // 检查服务器实例ID是否已存在
        if (existsByServerId(createDTO.getServerId())) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "服务器实例ID已存在");
        }

        UserServerInstance userServerInstance = BeanCopyUtil.copyProperties(createDTO, UserServerInstance.class);
        
        // 设置默认值
        if (userServerInstance.getInstanceStatus() == null) {
            userServerInstance.setInstanceStatus(1); // 1-创建中
        }
        if (userServerInstance.getEndTime() == null) {
            userServerInstance.setEndTime(0L);
        }
        if (userServerInstance.getExpireTime() == null) {
            userServerInstance.setExpireTime(0L);
        }
        if (userServerInstance.getAutoRenew() == null) {
            userServerInstance.setAutoRenew(0); // 0-否
        }
        if (!StringUtils.hasText(userServerInstance.getZoneId())) {
            userServerInstance.setZoneId("");
        }

        UserServerInstance createdUserServerInstance = createEntity(userServerInstance);
        return BeanCopyUtil.copyProperties(createdUserServerInstance, UserServerInstanceResponseDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserServerInstanceResponseDTO updateUserServerInstance(UserServerInstanceUpdateDTO updateDTO) {
        UserServerInstance existingUserServerInstance = getEntityById(updateDTO.getId());

        // 复制属性（忽略null值和不可修改的字段）
        BeanCopyUtil.copyPropertiesToExisting(updateDTO, existingUserServerInstance, 
            "userId", "orderId", "serverId", "serverSkuId", "billingType", 
            "billingCycle", "startTime", "regionId");

        UserServerInstance updatedUserServerInstance = updateEntity(existingUserServerInstance);
        return BeanCopyUtil.copyProperties(updatedUserServerInstance, UserServerInstanceResponseDTO.class);
    }

    @Override
    public UserServerInstanceResponseDTO getUserServerInstanceById(Long id) {
        UserServerInstance userServerInstance = getEntityById(id);
        return BeanCopyUtil.copyProperties(userServerInstance, UserServerInstanceResponseDTO.class);
    }

    @Override
    public IPage<UserServerInstanceResponseDTO> getUserServerInstancePage(UserServerInstanceQueryDTO queryDTO) {
        IPage<UserServerInstance> page = queryDTO.page();
        QueryWrapper<UserServerInstance> queryWrapper = buildUserServerInstanceQueryWrapper(queryDTO);
        
        IPage<UserServerInstance> userServerInstancePage = page(page, queryWrapper);
        
        // 转换为响应DTO
        List<UserServerInstanceResponseDTO> responseDTOList = BeanCopyUtil.copyPropertiesList(
            userServerInstancePage.getRecords(), UserServerInstanceResponseDTO.class);
        
        IPage<UserServerInstanceResponseDTO> responsePage = new Page<>(
            userServerInstancePage.getCurrent(), userServerInstancePage.getSize(), userServerInstancePage.getTotal());
        responsePage.setRecords(responseDTOList);
        
        return responsePage;
    }

    @Override
    public List<UserServerInstanceResponseDTO> getUserServerInstanceList(UserServerInstanceQueryDTO queryDTO) {
        QueryWrapper<UserServerInstance> queryWrapper = buildUserServerInstanceQueryWrapper(queryDTO);
        List<UserServerInstance> userServerInstanceList = list(queryWrapper);
        return BeanCopyUtil.copyPropertiesList(userServerInstanceList, UserServerInstanceResponseDTO.class);
    }

    @Override
    public UserServerInstance getUserServerInstanceByServerId(String serverId) {
        if (!StringUtils.hasText(serverId)) {
            return null;
        }
        QueryWrapper<UserServerInstance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("server_id", serverId);
        return getOne(queryWrapper);
    }

    @Override
    public List<UserServerInstance> getUserServerInstancesByUserId(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }
        QueryWrapper<UserServerInstance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByDesc("created_at");
        return list(queryWrapper);
    }

    @Override
    public List<UserServerInstance> getUserServerInstancesByUserIdAndStatus(Long userId, Integer instanceStatus) {
        if (userId == null || instanceStatus == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID和实例状态不能为空");
        }
        QueryWrapper<UserServerInstance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("instance_status", instanceStatus);
        queryWrapper.orderByDesc("created_at");
        return list(queryWrapper);
    }

    @Override
    public List<UserServerInstance> getUserServerInstancesByOrderId(Long orderId) {
        if (orderId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "订单ID不能为空");
        }
        QueryWrapper<UserServerInstance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_id", orderId);
        queryWrapper.orderByDesc("created_at");
        return list(queryWrapper);
    }

    @Override
    public List<UserServerInstance> getUserServerInstancesByRegionId(String regionId) {
        if (!StringUtils.hasText(regionId)) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "区域ID不能为空");
        }
        QueryWrapper<UserServerInstance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("region_id", regionId);
        queryWrapper.orderByDesc("created_at");
        return list(queryWrapper);
    }

    @Override
    public List<UserServerInstance> getUserServerInstancesByRegionIdAndZoneId(String regionId, String zoneId) {
        if (!StringUtils.hasText(regionId) || !StringUtils.hasText(zoneId)) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "区域ID和可用区ID不能为空");
        }
        QueryWrapper<UserServerInstance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("region_id", regionId);
        queryWrapper.eq("zone_id", zoneId);
        queryWrapper.orderByDesc("created_at");
        return list(queryWrapper);
    }

    @Override
    public boolean existsByServerId(String serverId) {
        if (!StringUtils.hasText(serverId)) {
            return false;
        }
        QueryWrapper<UserServerInstance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("server_id", serverId);
        return count(queryWrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startServerInstance(Long id) {
        UserServerInstance userServerInstance = getEntityById(id);
        
        // 检查实例状态
        if (userServerInstance.getInstanceStatus() == 2) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "实例已在运行中");
        }
        if (userServerInstance.getInstanceStatus() == 4) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "实例已销毁，无法启动");
        }
        
        // 更新实例状态
        userServerInstance.setInstanceStatus(2); // 2-运行中
        
        boolean success = updateById(userServerInstance);
        if (success) {
            log.info("启动服务器实例成功，实例ID: {}, 服务器ID: {}", id, userServerInstance.getServerId());
        }
        
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean stopServerInstance(Long id) {
        UserServerInstance userServerInstance = getEntityById(id);
        
        // 检查实例状态
        if (userServerInstance.getInstanceStatus() == 3) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "实例已停止");
        }
        if (userServerInstance.getInstanceStatus() == 4) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "实例已销毁，无法停止");
        }
        
        // 更新实例状态
        userServerInstance.setInstanceStatus(3); // 3-已停止
        
        boolean success = updateById(userServerInstance);
        if (success) {
            log.info("停止服务器实例成功，实例ID: {}, 服务器ID: {}", id, userServerInstance.getServerId());
        }
        
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean destroyServerInstance(Long id) {
        UserServerInstance userServerInstance = getEntityById(id);
        
        // 检查实例状态
        if (userServerInstance.getInstanceStatus() == 4) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "实例已销毁");
        }
        
        // 更新实例状态和结束计费时间
        userServerInstance.setInstanceStatus(4); // 4-已销毁
        if (userServerInstance.getEndTime() == 0) {
            userServerInstance.setEndTime(System.currentTimeMillis());
        }
        
        boolean success = updateById(userServerInstance);
        if (success) {
            log.info("销毁服务器实例成功，实例ID: {}, 服务器ID: {}", id, userServerInstance.getServerId());
        }
        
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean renewServerInstance(Long id, Long expireTime) {
        if (expireTime == null || expireTime <= System.currentTimeMillis()) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "到期时间必须大于当前时间");
        }
        
        UserServerInstance userServerInstance = getEntityById(id);
        
        // 检查计费类型
        if (userServerInstance.getBillingType() != 2) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "只有包年包月实例才能续费");
        }
        
        // 更新到期时间
        userServerInstance.setExpireTime(expireTime);
        
        boolean success = updateById(userServerInstance);
        if (success) {
            log.info("续费服务器实例成功，实例ID: {}, 服务器ID: {}, 新到期时间: {}", 
                    id, userServerInstance.getServerId(), expireTime);
        }
        
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setAutoRenew(Long id, Integer autoRenew) {
        if (autoRenew == null || (autoRenew != 0 && autoRenew != 1)) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "自动续费值必须为0或1");
        }
        
        UserServerInstance userServerInstance = getEntityById(id);
        
        // 检查计费类型
        if (userServerInstance.getBillingType() != 2) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "只有包年包月实例才能设置自动续费");
        }
        
        // 更新自动续费设置
        userServerInstance.setAutoRenew(autoRenew);
        
        boolean success = updateById(userServerInstance);
        if (success) {
            log.info("设置服务器实例自动续费成功，实例ID: {}, 服务器ID: {}, 自动续费: {}", 
                    id, userServerInstance.getServerId(), autoRenew == 1 ? "是" : "否");
        }
        
        return success;
    }

    @Override
    public List<UserServerInstance> getExpiringServerInstances(int days) {
        if (days <= 0) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "提前天数必须大于0");
        }

        long expireTimeThreshold = System.currentTimeMillis() + days * 24 * 60 * 60 * 1000L;

        QueryWrapper<UserServerInstance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("billing_type", 2); // 2-包年包月
        queryWrapper.in("instance_status", 1, 2, 3); // 1-创建中 2-运行中 3-已停止
        queryWrapper.gt("expire_time", System.currentTimeMillis()); // 未过期
        queryWrapper.le("expire_time", expireTimeThreshold); // 即将到期
        queryWrapper.orderByAsc("expire_time");

        return list(queryWrapper);
    }

    @Override
    public List<UserServerInstance> getExpiredServerInstances() {
        QueryWrapper<UserServerInstance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("billing_type", 2); // 2-包年包月
        queryWrapper.in("instance_status", 1, 2, 3); // 1-创建中 2-运行中 3-已停止
        queryWrapper.gt("expire_time", 0); // 有到期时间
        queryWrapper.lt("expire_time", System.currentTimeMillis()); // 已过期
        queryWrapper.orderByAsc("expire_time");

        return list(queryWrapper);
    }

    @Override
    public List<UserServerInstance> getAutoRenewServerInstances() {
        QueryWrapper<UserServerInstance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("billing_type", 2); // 2-包年包月
        queryWrapper.in("instance_status", 1, 2, 3); // 1-创建中 2-运行中 3-已停止
        queryWrapper.eq("auto_renew", 1); // 1-是
        queryWrapper.orderByAsc("expire_time");

        return list(queryWrapper);
    }

    @Override
    public long countUserServerInstances(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }

        QueryWrapper<UserServerInstance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        return count(queryWrapper);
    }

    @Override
    public long countUserRunningServerInstances(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }

        QueryWrapper<UserServerInstance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("instance_status", 2); // 2-运行中
        return count(queryWrapper);
    }

    @Override
    public long countUserStoppedServerInstances(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }

        QueryWrapper<UserServerInstance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("instance_status", 3); // 3-已停止
        return count(queryWrapper);
    }

    /**
     * 构建用户服务器实例查询条件
     */
    private QueryWrapper<UserServerInstance> buildUserServerInstanceQueryWrapper(UserServerInstanceQueryDTO queryDTO) {
        QueryWrapper<UserServerInstance> queryWrapper = new QueryWrapper<>();

        if (queryDTO.getId() != null) {
            queryWrapper.eq("id", queryDTO.getId());
        }
        if (queryDTO.getUserId() != null) {
            queryWrapper.eq("user_id", queryDTO.getUserId());
        }
        if (queryDTO.getOrderId() != null) {
            queryWrapper.eq("order_id", queryDTO.getOrderId());
        }
        if (StringUtils.hasText(queryDTO.getServerId())) {
            queryWrapper.eq("server_id", queryDTO.getServerId());
        }
        if (StringUtils.hasText(queryDTO.getServerIdLike())) {
            queryWrapper.like("server_id", queryDTO.getServerIdLike());
        }
        if (StringUtils.hasText(queryDTO.getServerName())) {
            queryWrapper.eq("server_name", queryDTO.getServerName());
        }
        if (StringUtils.hasText(queryDTO.getServerNameLike())) {
            queryWrapper.like("server_name", queryDTO.getServerNameLike());
        }
        if (StringUtils.hasText(queryDTO.getServerSkuId())) {
            queryWrapper.eq("server_sku_id", queryDTO.getServerSkuId());
        }
        if (StringUtils.hasText(queryDTO.getServerSkuIdLike())) {
            queryWrapper.like("server_sku_id", queryDTO.getServerSkuIdLike());
        }
        if (queryDTO.getBillingType() != null) {
            queryWrapper.eq("billing_type", queryDTO.getBillingType());
        }
        if (StringUtils.hasText(queryDTO.getBillingCycle())) {
            queryWrapper.eq("billing_cycle", queryDTO.getBillingCycle());
        }
        if (queryDTO.getInstanceStatus() != null) {
            queryWrapper.eq("instance_status", queryDTO.getInstanceStatus());
        }
        if (queryDTO.getStartTimeStart() != null) {
            queryWrapper.ge("start_time", queryDTO.getStartTimeStart());
        }
        if (queryDTO.getStartTimeEnd() != null) {
            queryWrapper.le("start_time", queryDTO.getStartTimeEnd());
        }
        if (queryDTO.getEndTimeStart() != null) {
            queryWrapper.ge("end_time", queryDTO.getEndTimeStart());
        }
        if (queryDTO.getEndTimeEnd() != null) {
            queryWrapper.le("end_time", queryDTO.getEndTimeEnd());
        }
        if (queryDTO.getExpireTimeStart() != null) {
            queryWrapper.ge("expire_time", queryDTO.getExpireTimeStart());
        }
        if (queryDTO.getExpireTimeEnd() != null) {
            queryWrapper.le("expire_time", queryDTO.getExpireTimeEnd());
        }
        if (queryDTO.getAutoRenew() != null) {
            queryWrapper.eq("auto_renew", queryDTO.getAutoRenew());
        }
        if (StringUtils.hasText(queryDTO.getRegionId())) {
            queryWrapper.eq("region_id", queryDTO.getRegionId());
        }
        if (StringUtils.hasText(queryDTO.getRegionIdLike())) {
            queryWrapper.like("region_id", queryDTO.getRegionIdLike());
        }
        if (StringUtils.hasText(queryDTO.getZoneId())) {
            queryWrapper.eq("zone_id", queryDTO.getZoneId());
        }
        if (StringUtils.hasText(queryDTO.getZoneIdLike())) {
            queryWrapper.like("zone_id", queryDTO.getZoneIdLike());
        }
        if (queryDTO.getCreatedAtStart() != null) {
            queryWrapper.ge("created_at", queryDTO.getCreatedAtStart());
        }
        if (queryDTO.getCreatedAtEnd() != null) {
            queryWrapper.le("created_at", queryDTO.getCreatedAtEnd());
        }
        if (queryDTO.getCreatedUid() != null) {
            queryWrapper.eq("created_uid", queryDTO.getCreatedUid());
        }

        queryWrapper.orderByDesc("created_at");
        return queryWrapper;
    }
}
