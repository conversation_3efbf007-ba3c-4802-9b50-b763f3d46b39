package com.cloudpod.podsail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.common.util.BeanCopyUtil;
import com.cloudpod.podsail.db.dao.UserApiKeyDao;
import com.cloudpod.podsail.db.entity.UserApiKey;
import com.cloudpod.podsail.db.mapper.UserApiKeyMapper;
import com.cloudpod.podsail.dto.user.UserApiKeyCreateDTO;
import com.cloudpod.podsail.dto.user.UserApiKeyQueryDTO;
import com.cloudpod.podsail.dto.user.UserApiKeyResponseDTO;
import com.cloudpod.podsail.dto.user.UserApiKeyUpdateDTO;
import com.cloudpod.podsail.service.UserApiKeyService;
import com.cloudpod.podsail.service.UserService;
import com.cloudpod.podsail.service.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.UUID;

/**
 * 用户API密钥服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Service
public class UserApiKeyServiceImpl extends BaseServiceImpl<UserApiKeyMapper, UserApiKey> implements UserApiKeyService {

    @Autowired
    private UserApiKeyDao userApiKeyDao;

    @Autowired
    private UserService userService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserApiKeyResponseDTO createUserApiKey(UserApiKeyCreateDTO createDTO) {
        // 检查用户是否存在
        userService.getEntityById(createDTO.getUserId());

        UserApiKey userApiKey = BeanCopyUtil.copyProperties(createDTO, UserApiKey.class);
        
        // 生成API密钥
        userApiKey.setApiKey(generateApiKey());
        
        // 设置默认状态为正常
        userApiKey.setStatus(1);
        
        // 设置最近访问时间为0
        userApiKey.setLastVisitTime(0L);

        UserApiKey createdUserApiKey = createEntity(userApiKey);
        return BeanCopyUtil.copyProperties(createdUserApiKey, UserApiKeyResponseDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserApiKeyResponseDTO updateUserApiKey(UserApiKeyUpdateDTO updateDTO) {
        UserApiKey existingUserApiKey = getEntityById(updateDTO.getId());

        // 复制属性（忽略null值和不可修改的字段）
        BeanCopyUtil.copyPropertiesToExisting(updateDTO, existingUserApiKey, "userId", "apiKey");

        UserApiKey updatedUserApiKey = updateEntity(existingUserApiKey);
        return BeanCopyUtil.copyProperties(updatedUserApiKey, UserApiKeyResponseDTO.class);
    }

    @Override
    public UserApiKeyResponseDTO getUserApiKeyById(Long id) {
        UserApiKey userApiKey = getEntityById(id);
        return BeanCopyUtil.copyProperties(userApiKey, UserApiKeyResponseDTO.class);
    }

    @Override
    public IPage<UserApiKeyResponseDTO> getUserApiKeyPage(UserApiKeyQueryDTO queryDTO) {
        IPage<UserApiKey> page = queryDTO.page();
        QueryWrapper<UserApiKey> queryWrapper = buildUserApiKeyQueryWrapper(queryDTO);
        
        IPage<UserApiKey> userApiKeyPage = page(page, queryWrapper);
        
        // 转换为响应DTO
        List<UserApiKeyResponseDTO> responseDTOList = BeanCopyUtil.copyPropertiesList(
            userApiKeyPage.getRecords(), UserApiKeyResponseDTO.class);
        
        IPage<UserApiKeyResponseDTO> responsePage = new Page<>(
            userApiKeyPage.getCurrent(), userApiKeyPage.getSize(), userApiKeyPage.getTotal());
        responsePage.setRecords(responseDTOList);
        
        return responsePage;
    }

    @Override
    public List<UserApiKeyResponseDTO> getUserApiKeyList(UserApiKeyQueryDTO queryDTO) {
        QueryWrapper<UserApiKey> queryWrapper = buildUserApiKeyQueryWrapper(queryDTO);
        List<UserApiKey> userApiKeyList = list(queryWrapper);
        return BeanCopyUtil.copyPropertiesList(userApiKeyList, UserApiKeyResponseDTO.class);
    }

    @Override
    public UserApiKey getUserApiKeyByApiKey(String apiKey) {
        if (!StringUtils.hasText(apiKey)) {
            return null;
        }
        QueryWrapper<UserApiKey> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("api_key", apiKey);
        return getOne(queryWrapper);
    }

    @Override
    public List<UserApiKey> getUserApiKeysByUserId(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }
        QueryWrapper<UserApiKey> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByDesc("created_at");
        return list(queryWrapper);
    }

    @Override
    public boolean existsByApiKey(String apiKey) {
        if (!StringUtils.hasText(apiKey)) {
            return false;
        }
        QueryWrapper<UserApiKey> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("api_key", apiKey);
        return count(queryWrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean revokeUserApiKey(Long id) {
        UserApiKey userApiKey = getEntityById(id);
        userApiKey.setStatus(2); // 2-吊销
        return updateById(userApiKey);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean activateUserApiKey(Long id) {
        UserApiKey userApiKey = getEntityById(id);
        userApiKey.setStatus(1); // 1-正常
        return updateById(userApiKey);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLastVisitTime(String apiKey) {
        if (!StringUtils.hasText(apiKey)) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "API密钥不能为空");
        }
        
        UserApiKey userApiKey = getUserApiKeyByApiKey(apiKey);
        if (userApiKey == null) {
            return false;
        }
        
        userApiKey.setLastVisitTime(System.currentTimeMillis());
        return updateById(userApiKey);
    }

    @Override
    public boolean validateApiKey(String apiKey) {
        if (!StringUtils.hasText(apiKey)) {
            return false;
        }
        
        UserApiKey userApiKey = getUserApiKeyByApiKey(apiKey);
        if (userApiKey == null) {
            return false;
        }
        
        // 检查状态是否为正常
        return userApiKey.getStatus() != null && userApiKey.getStatus() == 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String regenerateApiKey(Long id) {
        UserApiKey userApiKey = getEntityById(id);
        
        String newApiKey = generateApiKey();
        userApiKey.setApiKey(newApiKey);
        
        boolean success = updateById(userApiKey);
        if (!success) {
            throw new PodSailException(PodSailErrorCodeEnum.SYSTEM_ERROR, "重新生成API密钥失败");
        }
        
        log.info("重新生成API密钥成功，ID: {}", id);
        return newApiKey;
    }

    /**
     * 构建用户API密钥查询条件
     */
    private QueryWrapper<UserApiKey> buildUserApiKeyQueryWrapper(UserApiKeyQueryDTO queryDTO) {
        QueryWrapper<UserApiKey> queryWrapper = new QueryWrapper<>();
        
        if (queryDTO.getId() != null) {
            queryWrapper.eq("id", queryDTO.getId());
        }
        if (queryDTO.getUserId() != null) {
            queryWrapper.eq("user_id", queryDTO.getUserId());
        }
        if (StringUtils.hasText(queryDTO.getApiKey())) {
            queryWrapper.eq("api_key", queryDTO.getApiKey());
        }
        if (StringUtils.hasText(queryDTO.getApiKeyLike())) {
            queryWrapper.like("api_key", queryDTO.getApiKeyLike());
        }
        if (StringUtils.hasText(queryDTO.getRemark())) {
            queryWrapper.eq("remark", queryDTO.getRemark());
        }
        if (StringUtils.hasText(queryDTO.getRemarkLike())) {
            queryWrapper.like("remark", queryDTO.getRemarkLike());
        }
        if (queryDTO.getStatus() != null) {
            queryWrapper.eq("status", queryDTO.getStatus());
        }
        if (queryDTO.getLastVisitTimeStart() != null) {
            queryWrapper.ge("last_visit_time", queryDTO.getLastVisitTimeStart());
        }
        if (queryDTO.getLastVisitTimeEnd() != null) {
            queryWrapper.le("last_visit_time", queryDTO.getLastVisitTimeEnd());
        }
        if (queryDTO.getCreatedAtStart() != null) {
            queryWrapper.ge("created_at", queryDTO.getCreatedAtStart());
        }
        if (queryDTO.getCreatedAtEnd() != null) {
            queryWrapper.le("created_at", queryDTO.getCreatedAtEnd());
        }
        if (queryDTO.getCreatedUid() != null) {
            queryWrapper.eq("created_uid", queryDTO.getCreatedUid());
        }
        
        queryWrapper.orderByDesc("created_at");
        return queryWrapper;
    }

    /**
     * 生成API密钥
     */
    private String generateApiKey() {
        String apiKey;
        do {
            apiKey = "ak_" + UUID.randomUUID().toString().replace("-", "");
        } while (existsByApiKey(apiKey));
        return apiKey;
    }
}
