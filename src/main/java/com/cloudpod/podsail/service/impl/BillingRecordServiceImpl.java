package com.cloudpod.podsail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.common.util.BeanCopyUtil;
import com.cloudpod.podsail.db.dao.BillingRecordDao;
import com.cloudpod.podsail.db.entity.BillingRecord;
import com.cloudpod.podsail.db.mapper.BillingRecordMapper;
import com.cloudpod.podsail.dto.billing.BillingRecordCreateDTO;
import com.cloudpod.podsail.dto.billing.BillingRecordQueryDTO;
import com.cloudpod.podsail.dto.billing.BillingRecordResponseDTO;
import com.cloudpod.podsail.dto.billing.BillingRecordUpdateDTO;
import com.cloudpod.podsail.service.BillingRecordService;
import com.cloudpod.podsail.service.UserServerInstanceService;
import com.cloudpod.podsail.service.UserService;
import com.cloudpod.podsail.service.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 计费记录服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Service
public class BillingRecordServiceImpl extends BaseServiceImpl<BillingRecordMapper, BillingRecord> implements BillingRecordService {

    @Autowired
    private BillingRecordDao billingRecordDao;

    @Autowired
    private UserService userService;

    @Autowired
    private UserServerInstanceService userServerInstanceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BillingRecordResponseDTO createBillingRecord(BillingRecordCreateDTO createDTO) {
        // 检查用户是否存在
        userService.getEntityById(createDTO.getUserId());

        // 检查服务器实例是否存在
        userServerInstanceService.getEntityById(createDTO.getServerInstanceId());

        BillingRecord billingRecord = BeanCopyUtil.copyProperties(createDTO, BillingRecord.class);
        
        // 设置默认值
        if (!StringUtils.hasText(billingRecord.getCurrency())) {
            billingRecord.setCurrency("CNY");
        }
        if (billingRecord.getStatus() == null) {
            billingRecord.setStatus(1); // 1-待扣费
        }
        if (billingRecord.getChargeTime() == null) {
            billingRecord.setChargeTime(0L);
        }

        BillingRecord createdBillingRecord = createEntity(billingRecord);
        return BeanCopyUtil.copyProperties(createdBillingRecord, BillingRecordResponseDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BillingRecordResponseDTO updateBillingRecord(BillingRecordUpdateDTO updateDTO) {
        BillingRecord existingBillingRecord = getEntityById(updateDTO.getId());

        // 复制属性（忽略null值和不可修改的字段）
        BeanCopyUtil.copyPropertiesToExisting(updateDTO, existingBillingRecord, 
            "userId", "serverInstanceId", "billingType", "billingCycle", 
            "startTime", "endTime", "amount", "currency");

        BillingRecord updatedBillingRecord = updateEntity(existingBillingRecord);
        return BeanCopyUtil.copyProperties(updatedBillingRecord, BillingRecordResponseDTO.class);
    }

    @Override
    public BillingRecordResponseDTO getBillingRecordById(Long id) {
        BillingRecord billingRecord = getEntityById(id);
        return BeanCopyUtil.copyProperties(billingRecord, BillingRecordResponseDTO.class);
    }

    @Override
    public IPage<BillingRecordResponseDTO> getBillingRecordPage(BillingRecordQueryDTO queryDTO) {
        IPage<BillingRecord> page = queryDTO.page();
        QueryWrapper<BillingRecord> queryWrapper = buildBillingRecordQueryWrapper(queryDTO);
        
        IPage<BillingRecord> billingRecordPage = page(page, queryWrapper);
        
        // 转换为响应DTO
        List<BillingRecordResponseDTO> responseDTOList = BeanCopyUtil.copyPropertiesList(
            billingRecordPage.getRecords(), BillingRecordResponseDTO.class);
        
        IPage<BillingRecordResponseDTO> responsePage = new Page<>(
            billingRecordPage.getCurrent(), billingRecordPage.getSize(), billingRecordPage.getTotal());
        responsePage.setRecords(responseDTOList);
        
        return responsePage;
    }

    @Override
    public List<BillingRecordResponseDTO> getBillingRecordList(BillingRecordQueryDTO queryDTO) {
        QueryWrapper<BillingRecord> queryWrapper = buildBillingRecordQueryWrapper(queryDTO);
        List<BillingRecord> billingRecordList = list(queryWrapper);
        return BeanCopyUtil.copyPropertiesList(billingRecordList, BillingRecordResponseDTO.class);
    }

    @Override
    public List<BillingRecord> getBillingRecordsByUserId(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }
        QueryWrapper<BillingRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByDesc("created_at");
        return list(queryWrapper);
    }

    @Override
    public List<BillingRecord> getBillingRecordsByServerInstanceId(Long serverInstanceId) {
        if (serverInstanceId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "服务器实例ID不能为空");
        }
        QueryWrapper<BillingRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("server_instance_id", serverInstanceId);
        queryWrapper.orderByDesc("created_at");
        return list(queryWrapper);
    }

    @Override
    public List<BillingRecord> getBillingRecordsByUserIdAndStatus(Long userId, Integer status) {
        if (userId == null || status == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID和计费状态不能为空");
        }
        QueryWrapper<BillingRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("status", status);
        queryWrapper.orderByDesc("created_at");
        return list(queryWrapper);
    }

    @Override
    public List<BillingRecord> getBillingRecordsByServerInstanceIdAndStatus(Long serverInstanceId, Integer status) {
        if (serverInstanceId == null || status == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "服务器实例ID和计费状态不能为空");
        }
        QueryWrapper<BillingRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("server_instance_id", serverInstanceId);
        queryWrapper.eq("status", status);
        queryWrapper.orderByDesc("created_at");
        return list(queryWrapper);
    }

    @Override
    public List<BillingRecord> getPendingBillingRecords() {
        QueryWrapper<BillingRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1); // 1-待扣费
        queryWrapper.orderByAsc("created_at");
        return list(queryWrapper);
    }

    @Override
    public List<BillingRecord> getFailedBillingRecords() {
        QueryWrapper<BillingRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 3); // 3-扣费失败
        queryWrapper.orderByAsc("created_at");
        return list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean chargeSuccess(Long id) {
        BillingRecord billingRecord = getEntityById(id);
        
        // 检查计费状态
        if (billingRecord.getStatus() != 1) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "只有待扣费记录才能标记为扣费成功");
        }
        
        // 更新计费状态和扣费时间
        billingRecord.setStatus(2); // 2-已扣费
        billingRecord.setChargeTime(System.currentTimeMillis());
        
        boolean success = updateById(billingRecord);
        if (success) {
            log.info("计费记录扣费成功，记录ID: {}", id);
        }
        
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean chargeFailed(Long id, String remark) {
        BillingRecord billingRecord = getEntityById(id);
        
        // 检查计费状态
        if (billingRecord.getStatus() != 1) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "只有待扣费记录才能标记为扣费失败");
        }
        
        // 更新计费状态、扣费时间和备注
        billingRecord.setStatus(3); // 3-扣费失败
        billingRecord.setChargeTime(System.currentTimeMillis());
        if (StringUtils.hasText(remark)) {
            billingRecord.setRemark(remark);
        }
        
        boolean success = updateById(billingRecord);
        if (success) {
            log.info("计费记录扣费失败，记录ID: {}, 失败原因: {}", id, remark);
        }
        
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchChargeSuccess(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "ID列表不能为空");
        }
        
        int successCount = 0;
        for (Long id : ids) {
            try {
                chargeSuccess(id);
                successCount++;
            } catch (Exception e) {
                log.error("批量扣费成功失败，记录ID: {}", id, e);
            }
        }
        
        log.info("批量扣费成功完成，总数: {}, 成功: {}", ids.size(), successCount);
        return successCount;
    }

    @Override
    public BigDecimal getTotalBillingAmount(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }

        List<BillingRecord> billingRecords = getBillingRecordsByUserId(userId);
        return billingRecords.stream()
                .map(BillingRecord::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal getChargedAmount(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }

        List<BillingRecord> chargedRecords = getBillingRecordsByUserIdAndStatus(userId, 2); // 2-已扣费
        return chargedRecords.stream()
                .map(BillingRecord::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal getPendingAmount(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }

        List<BillingRecord> pendingRecords = getBillingRecordsByUserIdAndStatus(userId, 1); // 1-待扣费
        return pendingRecords.stream()
                .map(BillingRecord::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal getServerInstanceTotalBillingAmount(Long serverInstanceId) {
        if (serverInstanceId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "服务器实例ID不能为空");
        }

        List<BillingRecord> billingRecords = getBillingRecordsByServerInstanceId(serverInstanceId);
        return billingRecords.stream()
                .map(BillingRecord::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public long countUserBillingRecords(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }

        QueryWrapper<BillingRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        return count(queryWrapper);
    }

    @Override
    public long countUserChargedRecords(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }

        QueryWrapper<BillingRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("status", 2); // 2-已扣费
        return count(queryWrapper);
    }

    @Override
    public long countUserPendingRecords(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }

        QueryWrapper<BillingRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("status", 1); // 1-待扣费
        return count(queryWrapper);
    }

    /**
     * 构建计费记录查询条件
     */
    private QueryWrapper<BillingRecord> buildBillingRecordQueryWrapper(BillingRecordQueryDTO queryDTO) {
        QueryWrapper<BillingRecord> queryWrapper = new QueryWrapper<>();

        if (queryDTO.getId() != null) {
            queryWrapper.eq("id", queryDTO.getId());
        }
        if (queryDTO.getUserId() != null) {
            queryWrapper.eq("user_id", queryDTO.getUserId());
        }
        if (queryDTO.getServerInstanceId() != null) {
            queryWrapper.eq("server_instance_id", queryDTO.getServerInstanceId());
        }
        if (queryDTO.getBillingType() != null) {
            queryWrapper.eq("billing_type", queryDTO.getBillingType());
        }
        if (StringUtils.hasText(queryDTO.getBillingCycle())) {
            queryWrapper.eq("billing_cycle", queryDTO.getBillingCycle());
        }
        if (queryDTO.getStartTimeStart() != null) {
            queryWrapper.ge("start_time", queryDTO.getStartTimeStart());
        }
        if (queryDTO.getStartTimeEnd() != null) {
            queryWrapper.le("start_time", queryDTO.getStartTimeEnd());
        }
        if (queryDTO.getEndTimeStart() != null) {
            queryWrapper.ge("end_time", queryDTO.getEndTimeStart());
        }
        if (queryDTO.getEndTimeEnd() != null) {
            queryWrapper.le("end_time", queryDTO.getEndTimeEnd());
        }
        if (queryDTO.getAmountMin() != null) {
            queryWrapper.ge("amount", queryDTO.getAmountMin());
        }
        if (queryDTO.getAmountMax() != null) {
            queryWrapper.le("amount", queryDTO.getAmountMax());
        }
        if (StringUtils.hasText(queryDTO.getCurrency())) {
            queryWrapper.eq("currency", queryDTO.getCurrency());
        }
        if (queryDTO.getStatus() != null) {
            queryWrapper.eq("status", queryDTO.getStatus());
        }
        if (queryDTO.getChargeTimeStart() != null) {
            queryWrapper.ge("charge_time", queryDTO.getChargeTimeStart());
        }
        if (queryDTO.getChargeTimeEnd() != null) {
            queryWrapper.le("charge_time", queryDTO.getChargeTimeEnd());
        }
        if (StringUtils.hasText(queryDTO.getRemark())) {
            queryWrapper.eq("remark", queryDTO.getRemark());
        }
        if (StringUtils.hasText(queryDTO.getRemarkLike())) {
            queryWrapper.like("remark", queryDTO.getRemarkLike());
        }
        if (queryDTO.getCreatedAtStart() != null) {
            queryWrapper.ge("created_at", queryDTO.getCreatedAtStart());
        }
        if (queryDTO.getCreatedAtEnd() != null) {
            queryWrapper.le("created_at", queryDTO.getCreatedAtEnd());
        }
        if (queryDTO.getCreatedUid() != null) {
            queryWrapper.eq("created_uid", queryDTO.getCreatedUid());
        }

        queryWrapper.orderByDesc("created_at");
        return queryWrapper;
    }
}
