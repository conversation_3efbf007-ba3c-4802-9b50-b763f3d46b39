package com.cloudpod.podsail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.common.util.BeanCopyUtil;
import com.cloudpod.podsail.db.dao.UserOrderDao;
import com.cloudpod.podsail.db.entity.UserOrder;
import com.cloudpod.podsail.db.mapper.UserOrderMapper;
import com.cloudpod.podsail.dto.order.UserOrderCreateDTO;
import com.cloudpod.podsail.dto.order.UserOrderQueryDTO;
import com.cloudpod.podsail.dto.order.UserOrderResponseDTO;
import com.cloudpod.podsail.dto.order.UserOrderUpdateDTO;
import com.cloudpod.podsail.service.UserOrderService;
import com.cloudpod.podsail.service.UserService;
import com.cloudpod.podsail.service.base.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 用户订单服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Service
public class UserOrderServiceImpl extends BaseServiceImpl<UserOrderMapper, UserOrder> implements UserOrderService {

    @Autowired
    private UserOrderDao userOrderDao;

    @Autowired
    private UserService userService;

    private static final AtomicLong ORDER_SEQUENCE = new AtomicLong(1);

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserOrderResponseDTO createUserOrder(UserOrderCreateDTO createDTO) {
        // 检查用户是否存在
        userService.getEntityById(createDTO.getUserId());

        UserOrder userOrder = BeanCopyUtil.copyProperties(createDTO, UserOrder.class);
        
        // 生成订单号
        userOrder.setOrderNo(generateOrderNo());
        
        // 设置默认值
        if (userOrder.getQuantity() == null) {
            userOrder.setQuantity(1);
        }
        if (userOrder.getDiscountAmount() == null) {
            userOrder.setDiscountAmount(BigDecimal.ZERO);
        }
        if (!StringUtils.hasText(userOrder.getCurrency())) {
            userOrder.setCurrency("CNY");
        }
        
        // 设置默认状态
        userOrder.setOrderStatus(1); // 1-待支付
        userOrder.setPayStatus(1);   // 1-未支付
        userOrder.setPayTime(0L);
        
        // 设置默认过期时间（如果未提供，默认30分钟后过期）
        if (userOrder.getExpireTime() == null) {
            userOrder.setExpireTime(System.currentTimeMillis() + 30 * 60 * 1000L);
        }

        UserOrder createdUserOrder = createEntity(userOrder);
        return BeanCopyUtil.copyProperties(createdUserOrder, UserOrderResponseDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserOrderResponseDTO updateUserOrder(UserOrderUpdateDTO updateDTO) {
        UserOrder existingUserOrder = getEntityById(updateDTO.getId());

        // 复制属性（忽略null值和不可修改的字段）
        BeanCopyUtil.copyPropertiesToExisting(updateDTO, existingUserOrder, 
            "userId", "orderNo", "serverSkuId", "billingMethodId", "billingType", 
            "billingCycle", "quantity", "unitPrice", "totalAmount", "currency");

        UserOrder updatedUserOrder = updateEntity(existingUserOrder);
        return BeanCopyUtil.copyProperties(updatedUserOrder, UserOrderResponseDTO.class);
    }

    @Override
    public UserOrderResponseDTO getUserOrderById(Long id) {
        UserOrder userOrder = getEntityById(id);
        return BeanCopyUtil.copyProperties(userOrder, UserOrderResponseDTO.class);
    }

    @Override
    public IPage<UserOrderResponseDTO> getUserOrderPage(UserOrderQueryDTO queryDTO) {
        IPage<UserOrder> page = queryDTO.page();
        QueryWrapper<UserOrder> queryWrapper = buildUserOrderQueryWrapper(queryDTO);
        
        IPage<UserOrder> userOrderPage = page(page, queryWrapper);
        
        // 转换为响应DTO
        List<UserOrderResponseDTO> responseDTOList = BeanCopyUtil.copyPropertiesList(
            userOrderPage.getRecords(), UserOrderResponseDTO.class);
        
        IPage<UserOrderResponseDTO> responsePage = new Page<>(
            userOrderPage.getCurrent(), userOrderPage.getSize(), userOrderPage.getTotal());
        responsePage.setRecords(responseDTOList);
        
        return responsePage;
    }

    @Override
    public List<UserOrderResponseDTO> getUserOrderList(UserOrderQueryDTO queryDTO) {
        QueryWrapper<UserOrder> queryWrapper = buildUserOrderQueryWrapper(queryDTO);
        List<UserOrder> userOrderList = list(queryWrapper);
        return BeanCopyUtil.copyPropertiesList(userOrderList, UserOrderResponseDTO.class);
    }

    @Override
    public UserOrder getUserOrderByOrderNo(String orderNo) {
        if (!StringUtils.hasText(orderNo)) {
            return null;
        }
        QueryWrapper<UserOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", orderNo);
        return getOne(queryWrapper);
    }

    @Override
    public List<UserOrder> getUserOrdersByUserId(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }
        QueryWrapper<UserOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByDesc("created_at");
        return list(queryWrapper);
    }

    @Override
    public List<UserOrder> getUserOrdersByUserIdAndStatus(Long userId, Integer orderStatus) {
        if (userId == null || orderStatus == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID和订单状态不能为空");
        }
        QueryWrapper<UserOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("order_status", orderStatus);
        queryWrapper.orderByDesc("created_at");
        return list(queryWrapper);
    }

    @Override
    public boolean existsByOrderNo(String orderNo) {
        if (!StringUtils.hasText(orderNo)) {
            return false;
        }
        QueryWrapper<UserOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", orderNo);
        return count(queryWrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean payOrder(Long orderId) {
        UserOrder userOrder = getEntityById(orderId);
        
        // 检查订单状态
        if (userOrder.getOrderStatus() != 1) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "订单状态不是待支付，无法支付");
        }
        
        // 检查订单是否过期
        if (userOrder.getExpireTime() != null && userOrder.getExpireTime() > 0 && 
            System.currentTimeMillis() > userOrder.getExpireTime()) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "订单已过期，无法支付");
        }
        
        // 更新订单状态
        userOrder.setOrderStatus(2); // 2-已支付
        userOrder.setPayStatus(2);   // 2-已支付
        userOrder.setPayTime(System.currentTimeMillis());
        
        boolean success = updateById(userOrder);
        if (success) {
            log.info("订单支付成功，订单ID: {}, 订单号: {}", orderId, userOrder.getOrderNo());
        }
        
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelOrder(Long orderId) {
        UserOrder userOrder = getEntityById(orderId);
        
        // 检查订单状态
        if (userOrder.getOrderStatus() != 1) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "只有待支付订单才能取消");
        }
        
        // 更新订单状态
        userOrder.setOrderStatus(3); // 3-已取消
        
        boolean success = updateById(userOrder);
        if (success) {
            log.info("订单取消成功，订单ID: {}, 订单号: {}", orderId, userOrder.getOrderNo());
        }
        
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean refundOrder(Long orderId) {
        UserOrder userOrder = getEntityById(orderId);
        
        // 检查订单状态
        if (userOrder.getOrderStatus() != 2) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "只有已支付订单才能退款");
        }
        
        // 更新订单状态
        userOrder.setOrderStatus(4); // 4-已退款
        
        boolean success = updateById(userOrder);
        if (success) {
            log.info("订单退款成功，订单ID: {}, 订单号: {}", orderId, userOrder.getOrderNo());
        }
        
        return success;
    }

    @Override
    public List<UserOrder> getExpiredUnpaidOrders() {
        QueryWrapper<UserOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_status", 1); // 待支付
        queryWrapper.eq("pay_status", 1);   // 未支付
        queryWrapper.gt("expire_time", 0);  // 有过期时间
        queryWrapper.lt("expire_time", System.currentTimeMillis()); // 已过期
        return list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int autoCancelExpiredOrders() {
        List<UserOrder> expiredOrders = getExpiredUnpaidOrders();
        if (expiredOrders.isEmpty()) {
            return 0;
        }
        
        int canceledCount = 0;
        for (UserOrder order : expiredOrders) {
            try {
                cancelOrder(order.getId());
                canceledCount++;
            } catch (Exception e) {
                log.error("自动取消过期订单失败，订单ID: {}, 订单号: {}", order.getId(), order.getOrderNo(), e);
            }
        }
        
        log.info("自动取消过期订单完成，总数: {}, 成功: {}", expiredOrders.size(), canceledCount);
        return canceledCount;
    }

    @Override
    public BigDecimal getTotalOrderAmount(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }
        
        List<UserOrder> userOrders = getUserOrdersByUserId(userId);
        return userOrders.stream()
                .map(UserOrder::getActualAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal getPaidOrderAmount(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }
        
        List<UserOrder> paidOrders = getUserOrdersByUserIdAndStatus(userId, 2); // 2-已支付
        return paidOrders.stream()
                .map(UserOrder::getActualAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public long countUserOrders(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }
        
        QueryWrapper<UserOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        return count(queryWrapper);
    }

    @Override
    public long countUserPaidOrders(Long userId) {
        if (userId == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "用户ID不能为空");
        }
        
        QueryWrapper<UserOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("order_status", 2); // 2-已支付
        return count(queryWrapper);
    }

    /**
     * 构建用户订单查询条件
     */
    private QueryWrapper<UserOrder> buildUserOrderQueryWrapper(UserOrderQueryDTO queryDTO) {
        QueryWrapper<UserOrder> queryWrapper = new QueryWrapper<>();

        if (queryDTO.getId() != null) {
            queryWrapper.eq("id", queryDTO.getId());
        }
        if (queryDTO.getUserId() != null) {
            queryWrapper.eq("user_id", queryDTO.getUserId());
        }
        if (StringUtils.hasText(queryDTO.getOrderNo())) {
            queryWrapper.eq("order_no", queryDTO.getOrderNo());
        }
        if (StringUtils.hasText(queryDTO.getOrderNoLike())) {
            queryWrapper.like("order_no", queryDTO.getOrderNoLike());
        }
        if (StringUtils.hasText(queryDTO.getServerSkuId())) {
            queryWrapper.eq("server_sku_id", queryDTO.getServerSkuId());
        }
        if (StringUtils.hasText(queryDTO.getServerSkuIdLike())) {
            queryWrapper.like("server_sku_id", queryDTO.getServerSkuIdLike());
        }
        if (queryDTO.getBillingMethodId() != null) {
            queryWrapper.eq("billing_method_id", queryDTO.getBillingMethodId());
        }
        if (queryDTO.getBillingType() != null) {
            queryWrapper.eq("billing_type", queryDTO.getBillingType());
        }
        if (StringUtils.hasText(queryDTO.getBillingCycle())) {
            queryWrapper.eq("billing_cycle", queryDTO.getBillingCycle());
        }
        if (queryDTO.getQuantityMin() != null) {
            queryWrapper.ge("quantity", queryDTO.getQuantityMin());
        }
        if (queryDTO.getQuantityMax() != null) {
            queryWrapper.le("quantity", queryDTO.getQuantityMax());
        }
        if (queryDTO.getUnitPriceMin() != null) {
            queryWrapper.ge("unit_price", queryDTO.getUnitPriceMin());
        }
        if (queryDTO.getUnitPriceMax() != null) {
            queryWrapper.le("unit_price", queryDTO.getUnitPriceMax());
        }
        if (queryDTO.getTotalAmountMin() != null) {
            queryWrapper.ge("total_amount", queryDTO.getTotalAmountMin());
        }
        if (queryDTO.getTotalAmountMax() != null) {
            queryWrapper.le("total_amount", queryDTO.getTotalAmountMax());
        }
        if (queryDTO.getActualAmountMin() != null) {
            queryWrapper.ge("actual_amount", queryDTO.getActualAmountMin());
        }
        if (queryDTO.getActualAmountMax() != null) {
            queryWrapper.le("actual_amount", queryDTO.getActualAmountMax());
        }
        if (StringUtils.hasText(queryDTO.getCurrency())) {
            queryWrapper.eq("currency", queryDTO.getCurrency());
        }
        if (queryDTO.getOrderStatus() != null) {
            queryWrapper.eq("order_status", queryDTO.getOrderStatus());
        }
        if (queryDTO.getPayStatus() != null) {
            queryWrapper.eq("pay_status", queryDTO.getPayStatus());
        }
        if (queryDTO.getPayTimeStart() != null) {
            queryWrapper.ge("pay_time", queryDTO.getPayTimeStart());
        }
        if (queryDTO.getPayTimeEnd() != null) {
            queryWrapper.le("pay_time", queryDTO.getPayTimeEnd());
        }
        if (queryDTO.getExpireTimeStart() != null) {
            queryWrapper.ge("expire_time", queryDTO.getExpireTimeStart());
        }
        if (queryDTO.getExpireTimeEnd() != null) {
            queryWrapper.le("expire_time", queryDTO.getExpireTimeEnd());
        }
        if (StringUtils.hasText(queryDTO.getRemark())) {
            queryWrapper.eq("remark", queryDTO.getRemark());
        }
        if (StringUtils.hasText(queryDTO.getRemarkLike())) {
            queryWrapper.like("remark", queryDTO.getRemarkLike());
        }
        if (queryDTO.getCreatedAtStart() != null) {
            queryWrapper.ge("created_at", queryDTO.getCreatedAtStart());
        }
        if (queryDTO.getCreatedAtEnd() != null) {
            queryWrapper.le("created_at", queryDTO.getCreatedAtEnd());
        }
        if (queryDTO.getCreatedUid() != null) {
            queryWrapper.eq("created_uid", queryDTO.getCreatedUid());
        }

        queryWrapper.orderByDesc("created_at");
        return queryWrapper;
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        String orderNo;
        do {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String sequence = String.format("%04d", ORDER_SEQUENCE.getAndIncrement() % 10000);
            orderNo = "ORDER" + timestamp + sequence;
        } while (existsByOrderNo(orderNo));
        return orderNo;
    }
}
