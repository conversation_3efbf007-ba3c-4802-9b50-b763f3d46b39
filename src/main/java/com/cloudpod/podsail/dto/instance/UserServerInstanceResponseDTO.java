package com.cloudpod.podsail.dto.instance;

import com.cloudpod.podsail.common.base.dto.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户服务器实例响应DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "UserServerInstanceResponseDTO", description = "用户服务器实例响应数据")
public class UserServerInstanceResponseDTO extends EntityDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", example = "1")
    private Long userId;

    @ApiModelProperty(value = "订单ID", example = "1")
    private Long orderId;

    @ApiModelProperty(value = "服务器实例ID(cloudpods)", example = "server-001")
    private String serverId;

    @ApiModelProperty(value = "服务器名称", example = "web-server-01")
    private String serverName;

    @ApiModelProperty(value = "服务器套餐SKU ID", example = "sku-001")
    private String serverSkuId;

    @ApiModelProperty(value = "计费类型", example = "1", notes = "1-按需付费 2-包年包月")
    private Integer billingType;

    @ApiModelProperty(value = "计费类型描述", example = "按需付费")
    private String billingTypeDesc;

    @ApiModelProperty(value = "计费周期", example = "1H")
    private String billingCycle;

    @ApiModelProperty(value = "实例状态", example = "2", notes = "1-创建中 2-运行中 3-已停止 4-已销毁")
    private Integer instanceStatus;

    @ApiModelProperty(value = "实例状态描述", example = "运行中")
    private String instanceStatusDesc;

    @ApiModelProperty(value = "开始计费时间", example = "1692345600000")
    private Long startTime;

    @ApiModelProperty(value = "结束计费时间", example = "1692432000000")
    private Long endTime;

    @ApiModelProperty(value = "到期时间", example = "1692432000000")
    private Long expireTime;

    @ApiModelProperty(value = "是否自动续费", example = "0", notes = "0-否 1-是")
    private Integer autoRenew;

    @ApiModelProperty(value = "自动续费描述", example = "否")
    private String autoRenewDesc;

    @ApiModelProperty(value = "区域ID", example = "region-001")
    private String regionId;

    @ApiModelProperty(value = "可用区ID", example = "zone-001")
    private String zoneId;

    /**
     * 获取计费类型描述
     */
    public String getBillingTypeDesc() {
        if (billingType == null) {
            return "未知";
        }
        switch (billingType) {
            case 1:
                return "按需付费";
            case 2:
                return "包年包月";
            default:
                return "未知";
        }
    }

    /**
     * 获取实例状态描述
     */
    public String getInstanceStatusDesc() {
        if (instanceStatus == null) {
            return "未知";
        }
        switch (instanceStatus) {
            case 1:
                return "创建中";
            case 2:
                return "运行中";
            case 3:
                return "已停止";
            case 4:
                return "已销毁";
            default:
                return "未知";
        }
    }

    /**
     * 获取自动续费描述
     */
    public String getAutoRenewDesc() {
        if (autoRenew == null) {
            return "未知";
        }
        switch (autoRenew) {
            case 0:
                return "否";
            case 1:
                return "是";
            default:
                return "未知";
        }
    }
}
