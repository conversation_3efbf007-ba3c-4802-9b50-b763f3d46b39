package com.cloudpod.podsail.dto.billing;

import com.cloudpod.podsail.common.base.dto.BaseQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 主机套餐规格定价查询DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ServerSkuBillingMethodQueryDTO", description = "主机套餐规格定价查询请求参数")
public class ServerSkuBillingMethodQueryDTO extends BaseQueryDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "服务器套餐SKU ID", example = "sku-001")
    private String serverSkuId;

    @ApiModelProperty(value = "服务器套餐SKU ID模糊查询", example = "sku")
    private String serverSkuIdLike;

    @ApiModelProperty(value = "计费类型", example = "1", notes = "1-按需付费(postpaid) 2-包年包月(prepaid)")
    private Integer billingType;

    @ApiModelProperty(value = "计费周期", example = "1H")
    private String billingCycle;

    @ApiModelProperty(value = "价格最小值", example = "0.0000")
    private BigDecimal priceMin;

    @ApiModelProperty(value = "价格最大值", example = "1000.0000")
    private BigDecimal priceMax;

    @ApiModelProperty(value = "原价最小值", example = "0.0000")
    private BigDecimal originalPriceMin;

    @ApiModelProperty(value = "原价最大值", example = "1000.0000")
    private BigDecimal originalPriceMax;

    @ApiModelProperty(value = "折扣率最小值", example = "0.5000")
    private BigDecimal discountRateMin;

    @ApiModelProperty(value = "折扣率最大值", example = "1.0000")
    private BigDecimal discountRateMax;

    @ApiModelProperty(value = "货币单位", example = "CNY")
    private String currency;

    @ApiModelProperty(value = "区域ID", example = "region-001")
    private String regionId;

    @ApiModelProperty(value = "区域ID模糊查询", example = "region")
    private String regionIdLike;

    @ApiModelProperty(value = "可用区ID", example = "zone-001")
    private String zoneId;

    @ApiModelProperty(value = "可用区ID模糊查询", example = "zone")
    private String zoneIdLike;

    @ApiModelProperty(value = "生效时间开始", example = "1692345600000")
    private Long effectiveTimeStart;

    @ApiModelProperty(value = "生效时间结束", example = "1692432000000")
    private Long effectiveTimeEnd;

    @ApiModelProperty(value = "失效时间开始", example = "1692345600000")
    private Long expireTimeStart;

    @ApiModelProperty(value = "失效时间结束", example = "1692432000000")
    private Long expireTimeEnd;

    @ApiModelProperty(value = "状态", example = "1", notes = "1-正常 2-已失效")
    private Integer status;

    @ApiModelProperty(value = "备注", example = "测试定价")
    private String remark;

    @ApiModelProperty(value = "备注模糊查询", example = "测试")
    private String remarkLike;
}
