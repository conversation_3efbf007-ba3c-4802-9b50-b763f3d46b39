package com.cloudpod.podsail.dto.billing;

import com.cloudpod.podsail.common.base.dto.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 主机套餐规格定价响应DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ServerSkuBillingMethodResponseDTO", description = "主机套餐规格定价响应数据")
public class ServerSkuBillingMethodResponseDTO extends EntityDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "服务器套餐SKU ID", example = "sku-001")
    private String serverSkuId;

    @ApiModelProperty(value = "计费类型", example = "1", notes = "1-按需付费(postpaid) 2-包年包月(prepaid)")
    private Integer billingType;

    @ApiModelProperty(value = "计费类型描述", example = "按需付费")
    private String billingTypeDesc;

    @ApiModelProperty(value = "计费周期", example = "1H")
    private String billingCycle;

    @ApiModelProperty(value = "价格(元)", example = "10.0000")
    private BigDecimal price;

    @ApiModelProperty(value = "原价(元)", example = "12.0000")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "折扣率", example = "0.8333")
    private BigDecimal discountRate;

    @ApiModelProperty(value = "货币单位", example = "CNY")
    private String currency;

    @ApiModelProperty(value = "区域ID", example = "region-001")
    private String regionId;

    @ApiModelProperty(value = "可用区ID", example = "zone-001")
    private String zoneId;

    @ApiModelProperty(value = "生效时间", example = "1692345600000")
    private Long effectiveTime;

    @ApiModelProperty(value = "失效时间", example = "1692432000000")
    private Long expireTime;

    @ApiModelProperty(value = "状态", example = "1", notes = "1-正常 2-已失效")
    private Integer status;

    @ApiModelProperty(value = "状态描述", example = "正常")
    private String statusDesc;

    @ApiModelProperty(value = "备注", example = "测试定价")
    private String remark;

    /**
     * 获取计费类型描述
     */
    public String getBillingTypeDesc() {
        if (billingType == null) {
            return "未知";
        }
        switch (billingType) {
            case 1:
                return "按需付费";
            case 2:
                return "包年包月";
            default:
                return "未知";
        }
    }

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 1:
                return "正常";
            case 2:
                return "已失效";
            default:
                return "未知";
        }
    }
}
