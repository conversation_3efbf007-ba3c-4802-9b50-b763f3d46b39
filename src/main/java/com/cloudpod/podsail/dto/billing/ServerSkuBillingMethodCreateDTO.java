package com.cloudpod.podsail.dto.billing;

import com.cloudpod.podsail.common.base.dto.BaseCreateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 主机套餐规格定价创建DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ServerSkuBillingMethodCreateDTO", description = "主机套餐规格定价创建请求参数")
public class ServerSkuBillingMethodCreateDTO extends BaseCreateDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "服务器套餐SKU ID", required = true, example = "sku-001")
    @NotBlank(message = "服务器套餐SKU ID不能为空")
    @Size(max = 64, message = "服务器套餐SKU ID长度不能超过64个字符")
    private String serverSkuId;

    @ApiModelProperty(value = "计费类型", required = true, example = "1", notes = "1-按需付费(postpaid) 2-包年包月(prepaid)")
    @NotNull(message = "计费类型不能为空")
    @Min(value = 1, message = "计费类型值必须为1或2")
    @Max(value = 2, message = "计费类型值必须为1或2")
    private Integer billingType;

    @ApiModelProperty(value = "计费周期", required = true, example = "1H", notes = "按需付费:1H,1D 包年包月:1M,3M,6M,1Y,2Y,3Y")
    @NotBlank(message = "计费周期不能为空")
    @Size(max = 16, message = "计费周期长度不能超过16个字符")
    private String billingCycle;

    @ApiModelProperty(value = "价格(元)", required = true, example = "10.0000")
    @NotNull(message = "价格不能为空")
    @DecimalMin(value = "0.0001", message = "价格必须大于0")
    @Digits(integer = 16, fraction = 4, message = "价格格式不正确，最多16位整数，4位小数")
    private BigDecimal price;

    @ApiModelProperty(value = "原价(元)", required = true, example = "12.0000")
    @NotNull(message = "原价不能为空")
    @DecimalMin(value = "0.0001", message = "原价必须大于0")
    @Digits(integer = 16, fraction = 4, message = "原价格式不正确，最多16位整数，4位小数")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "折扣率", example = "0.8000", notes = "0.8表示8折")
    @DecimalMin(value = "0.0001", message = "折扣率必须大于0")
    @DecimalMax(value = "1.0000", message = "折扣率不能大于1")
    @Digits(integer = 1, fraction = 4, message = "折扣率格式不正确，最多1位整数，4位小数")
    private BigDecimal discountRate;

    @ApiModelProperty(value = "货币单位", example = "CNY", notes = "CNY-人民币 USD-美元")
    @Size(max = 8, message = "货币单位长度不能超过8个字符")
    private String currency;

    @ApiModelProperty(value = "区域ID", example = "region-001")
    @Size(max = 64, message = "区域ID长度不能超过64个字符")
    private String regionId;

    @ApiModelProperty(value = "可用区ID", example = "zone-001")
    @Size(max = 64, message = "可用区ID长度不能超过64个字符")
    private String zoneId;

    @ApiModelProperty(value = "生效时间", required = true, example = "1692345600000")
    @NotNull(message = "生效时间不能为空")
    private Long effectiveTime;

    @ApiModelProperty(value = "失效时间", example = "1692432000000", notes = "0表示永久有效")
    private Long expireTime;

    @ApiModelProperty(value = "状态", example = "1", notes = "1-正常 2-已失效")
    @Min(value = 1, message = "状态值必须为1或2")
    @Max(value = 2, message = "状态值必须为1或2")
    private Integer status;

    @ApiModelProperty(value = "备注", example = "测试定价")
    @Size(max = 256, message = "备注长度不能超过256个字符")
    private String remark;
}
